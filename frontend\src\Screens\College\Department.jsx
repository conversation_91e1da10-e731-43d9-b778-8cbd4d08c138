import React from "react";
import { FiArrowLeft } from "react-icons/fi";
import { useNavigate } from "react-router-dom";
import CollegeLayout from "../../components/Layout/CollegeLayout";
import { motion } from "framer-motion";

const departments = [
  "Civil",
  "Computer Science",
  "AIML",
  "CSD",
  "ISE",
  "IOT",
  "Mech"
];

const Department = () => {
  const navigate = useNavigate();
  
  return (
    <CollegeLayout>
      <nav className="w-full shadow flex items-center justify-between px-6 py-4 mb-4 bg-gradient-to-r" style={{ background: 'linear-gradient(90deg,rgb(35, 65, 75) 0%,rgb(35, 65, 75) 100%)' }}>
        <div className="flex items-center gap-3">
          <button onClick={() => navigate(-1)} className="text-white hover:bg-white/10 rounded-full p-2 mr-2" title="Back">
            <FiArrowLeft size={22} />
          </button>
        </div>
        <div>
          <img src="/images/Medini logo White.png" alt="Logo" className="h-10 rounded" />
        </div>
      </nav>
      <motion.div className="w-full min-h-screen bg-white p-8" style={{ backgroundColor: 'white' }}
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7 }}
      >
        <h1 className="text-3xl font-bold text-gray-800 mb-6">Departments</h1>
        <div className="w-full">
          <table className="min-w-full border border-gray-200 rounded-lg overflow-hidden">
            <thead className="bg-blue-50">
              <tr>
                <th className="px-6 py-3 text-left text-sm font-semibold text-gray-700">Department Name</th>
              </tr>
            </thead>
            <tbody>
              {departments.map((dept, idx) => (
                <tr key={dept} className="border-b last:border-b-0 hover:bg-blue-50/40 transition">
                  <td className="px-6 py-3 text-gray-800 font-medium">{dept}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.div>
    </CollegeLayout>
  );
};

export default Department; 