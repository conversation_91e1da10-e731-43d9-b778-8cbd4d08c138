import React, { useState, useEffect } from 'react';
import { FiMenu, FiX, FiGrid, FiUsers, FiUserCheck, FiDownload, FiLogOut } from 'react-icons/fi';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import { theme } from '../../theme';
import styled from 'styled-components';

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: ${theme.colors.background};
  color: ${theme.colors.text.primary};
`;

const Sidebar = styled.aside`
  width: 250px;
  background-color: ${theme.colors.surface};
  border-right: 1px solid ${theme.colors.border};
  position: fixed;
  top: 0;
  left: ${({ $isOpen }) => ($isOpen ? '0' : '-250px')};
  bottom: 0;
  z-index: 50;
  transition: left 0.3s ease;
  overflow-y: auto;
  @media (min-width: ${theme.breakpoints.tablet}) {
    left: 0;
  }
`;

const SidebarHeader = styled.div`
  padding: ${theme.spacing.lg};
  border-bottom: 1px solid ${theme.colors.border};
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: bold;
  color: ${theme.colors.secondary};
  justify-content: space-between;
`;

const MenuButton = styled.button`
  background: none;
  border: none;
  color: ${theme.colors.text.primary};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${theme.spacing.sm};
  border-radius: 50%;
  transition: all 0.2s ease;
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  @media (min-width: ${theme.breakpoints.tablet}) {
    display: none;
  }
`;

const MenuList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const MenuItem = styled.li`
  margin-bottom: ${theme.spacing.sm};
`;

const MenuLink = styled(Link)`
  display: flex;
  align-items: center;
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  color: ${({ $isActive }) => $isActive ? theme.colors.secondary : theme.colors.text.primary};
  text-decoration: none;
  border-radius: ${theme.borderRadius.md};
  background-color: ${({ $isActive }) => $isActive ? 'rgba(255, 255, 255, 0.1)' : 'transparent'};
  transition: all 0.2s ease;
  margin: 0 ${theme.spacing.sm};
  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  svg {
    margin-right: ${theme.spacing.sm};
  }
`;

const MainContent = styled.main`
  flex: 1;
  margin-left: 0;
  padding: ${theme.spacing.lg};
  transition: margin-left 0.3s ease;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  @media (min-width: ${theme.breakpoints.tablet}) {
    margin-left: 250px;
    width: calc(100% - 250px);
  }
`;

const MobileHeader = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${theme.spacing.lg};
  padding: ${theme.spacing.sm} 0;
  border-bottom: 1px solid ${theme.colors.border};
  h1 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
  }
  @media (min-width: ${theme.breakpoints.tablet}) {
    display: none;
  }
`;

const CollegeLayout = ({ children }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    { icon: <FiGrid size={20} />, label: 'Department', path: '/college/department' },
    { icon: <FiUsers size={20} />, label: 'CSE', path: '/college/cse' },
    { icon: <FiUsers size={20} />, label: 'AIML', path: '/college/aiml' },
    { icon: <FiUsers size={20} />, label: 'ISE', path: '/college/ise' },
    { icon: <FiUsers size={20} />, label: 'IOT', path: '/college/iot' },
    { icon: <FiUsers size={20} />, label: 'Civil', path: '/college/civil' },
    { icon: <FiUsers size={20} />, label: 'Mech', path: '/college/mech' },
    { icon: <FiUserCheck size={20} />, label: 'CSD', path: '/college/csd' },
  ];

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth >= 768) {
        setIsSidebarOpen(true);
      } else {
        setIsSidebarOpen(false);
      }
    };
    window.addEventListener('resize', handleResize);
    handleResize();
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    navigate('/login');
    if (isMobile) {
      setIsSidebarOpen(false);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isMobile && isSidebarOpen && !event.target.closest('aside')) {
        setIsSidebarOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, isSidebarOpen]);

  const isDepartmentPage = location.pathname === '/college/department';
  const isCSEPage = location.pathname === '/college/cse';
  const isAIMLPage = location.pathname === '/college/aiml';
  const isISEPage = location.pathname === '/college/ise';
  const isIOTPage = location.pathname === '/college/iot';
  const isCivilPage = location.pathname === '/college/civil';
  const isMechPage = location.pathname === '/college/mech';
  const isCSDPage = location.pathname === '/college/csd';
  const hideSidebar = isDepartmentPage || isCSEPage || isAIMLPage || isISEPage || isIOTPage || isCivilPage || isMechPage || isCSDPage;
  

  return (
    <LayoutContainer>
      {!hideSidebar && (
        <>
          {isMobile && isSidebarOpen && (
            <div 
              style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                zIndex: 40,
                display: 'block',
              }}
              onClick={() => setIsSidebarOpen(false)}
            />
          )}
          <Sidebar $isOpen={isSidebarOpen}>
            <SidebarHeader>
              <Link to="/college" style={{ textDecoration: 'none' }}>
                <img src="/images/Medini logo White.png" alt="Logo" className="h-8 rounded" />
              </Link>
              <MenuButton onClick={() => setIsSidebarOpen(false)}>
                <FiX size={24} />
              </MenuButton>
            </SidebarHeader>
            <nav>
              <MenuList>
                {menuItems.map((item) => (
                  <MenuItem key={item.path}>
                    <MenuLink
                      to={item.path}
                      $isActive={location.pathname === item.path}
                      onClick={() => isMobile && setIsSidebarOpen(false)}
                    >
                      {item.icon}
                      {item.label}
                    </MenuLink>
                  </MenuItem>
                ))}
              </MenuList>
            </nav>
            <div style={{
              padding: theme.spacing.md,
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              borderTop: `1px solid ${theme.colors.border}`,
            }}>
              <MenuLink 
                as="button"
                onClick={handleLogout}
                style={{
                  width: '100%',
                  textAlign: 'left',
                  cursor: 'pointer',
                }}
              >
                <FiLogOut />
                Logout
              </MenuLink>
            </div>
          </Sidebar>
        </>
      )}
      <MainContent style={{ marginLeft: hideSidebar ? 0 : undefined }}>
        {!hideSidebar && isMobile && (
          <MobileHeader>
            <MenuButton onClick={() => setIsSidebarOpen(true)}>
              <FiMenu size={24} />
            </MenuButton>
            <h1>
              {menuItems.find(item => location.pathname === item.path)?.label || 'College Panel'}
            </h1>
            <div style={{ width: '40px' }}></div>
          </MobileHeader>
        )}
        <div style={{
          backgroundColor: theme.colors.background,
          borderRadius: theme.borderRadius.lg,
          padding: isMobile ? theme.spacing.md : theme.spacing.lg,
          minHeight: 'calc(100vh - 120px)',
        }}>
          {children}
        </div>
      </MainContent>
    </LayoutContainer>
  );
};

export default CollegeLayout; 