import React, { useState } from 'react';
import { 
  FiBook<PERSON>pen, 
  FiUser, 
  FiLayers, 
  FiHome, 
  FiUsers,
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiEye,
  FiX,
  FiSave,
  FiRefreshCw,
  FiUpload,
  FiAward,
  FiMapPin
} from 'react-icons/fi';

const trainerOptions = ['Trainer A', 'Trainer B'];
const departmentOptions = ['CS', 'ECE', 'MECH'];
const collegeOptions = ['ABC College', 'XYZ Institute'];
const batchOptions = ['Batch A – 2024', 'Batch B – 2025'];

const initialCourses = [
  {
    id: 1,
    title: 'Python Basics',
    trainer: 'Trainer A',
    department: 'CS',
    college: 'ABC College',
    batch: 'Batch A – 2024',
  },
];

const UploadCourseWithTrainer = () => {
  const [title, setTitle] = useState('');
  const [trainer, setTrainer] = useState('');
  const [department, setDepartment] = useState('');
  const [college, setCollege] = useState('');
  const [batch, setBatch] = useState('');
  const [courses, setCourses] = useState(initialCourses);
  const [showModal, setShowModal] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    const newCourse = {
      id: Date.now(),
      title,
      trainer,
      department,
      college,
      batch,
    };
    setCourses([...courses, newCourse]);
    handleReset();
  };

  const handleReset = () => {
    setTitle('');
    setTrainer('');
    setDepartment('');
    setCollege('');
    setBatch('');
  };

  const openCourseModal = (course) => {
    setSelectedCourse(course);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedCourse(null);
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  return (
    <div className=" p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Upload Course With Trainer</h1>
            <p className="text-gray-700 mt-1">Create and manage course uploads with trainer assignments</p>
          </div>
          <button 
            onClick={handleEditToggle}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
              isEditing 
                ? "bg-red-600 text-white hover:bg-red-700" 
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
          >
            {isEditing ? <FiX size={16} /> : <FiPlus size={16} />}
            {isEditing ? "Cancel" : "Edit Mode"}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Course Upload Form */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Upload New Course</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiBookOpen className="mr-2 text-blue-500" />
                    Course Title
                  </label>
                  <input 
                    value={title} 
                    onChange={e => setTitle(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="Course Title" 
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiUser className="mr-2 text-yellow-500" />
                    Select Trainer
                  </label>
                  <select 
                    value={trainer} 
                    onChange={e => setTrainer(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Trainer</option>
                    {trainerOptions.map(t => <option key={t} value={t}>{t}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiAward className="mr-2 text-green-500" />
                    Department
                  </label>
                  <select 
                    value={department} 
                    onChange={e => setDepartment(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Department</option>
                    {departmentOptions.map(dep => <option key={dep} value={dep}>{dep}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiHome className="mr-2 text-red-500" />
                    Select College
                  </label>
                  <select 
                    value={college} 
                    onChange={e => setCollege(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select College</option>
                    {collegeOptions.map(c => <option key={c} value={c}>{c}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiUsers className="mr-2 text-purple-500" />
                    Select Batch
                  </label>
                  <select 
                    value={batch} 
                    onChange={e => setBatch(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Batch</option>
                    {batchOptions.map(b => <option key={b} value={b}>{b}</option>)}
                  </select>
                </div>

                <div className="flex gap-3 pt-4">
                  <button 
                    type="submit" 
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiUpload className="mr-2" />
                    Upload Course
                  </button>
                  <button 
                    type="button" 
                    onClick={handleReset} 
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiRefreshCw className="mr-2" />
                    Reset
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Right Panel - Course List */}
          <div className="lg:col-span-2">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
              <div className="border-b border-gray-200">
                <div className="flex space-x-8 px-6">
                  <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-blue-600 font-medium text-sm text-blue-600">
                    <FiBookOpen size={16} />
                    Uploaded Courses
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {courses.length === 0 ? (
                    <div className="text-center py-12">
                      <FiBookOpen className="text-6xl text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500 text-lg">No courses uploaded yet</p>
                      <p className="text-gray-400 text-sm mt-2">Upload your first course using the form</p>
                    </div>
                  ) : (
                    courses.map((course) => (
                      <div key={course.id} className="bg-gray-50 rounded-xl p-6 border-2 border-black hover:shadow-lg transition-all duration-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-4">
                              <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white shadow-lg">
                                {course.department}
                              </span>
                              <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                                {course.batch}
                              </span>
                            </div>
                            <h4 className="text-xl font-bold text-gray-900 mb-3">{course.title}</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiUser className="mr-3 text-yellow-500" />
                                <span className="font-medium">{course.trainer}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiMapPin className="mr-3 text-green-500" />
                                <span className="font-medium">{course.college}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiAward className="mr-3 text-green-500" />
                                <span className="font-medium">{course.department}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiUsers className="mr-3 text-purple-500" />
                                <span className="font-medium">{course.batch}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <button
                              onClick={() => openCourseModal(course)}
                              className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors border-2 border-blue-600"
                            >
                              <FiEye className="text-lg" />
                            </button>
                            <button
                              className="bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors border-2 border-green-600"
                            >
                              <FiEdit2 className="text-lg" />
                            </button>
                            <button
                              className="bg-red-600 text-white p-2 rounded-lg hover:bg-red-700 transition-colors border-2 border-red-600"
                            >
                              <FiTrash2 className="text-lg" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Course Details Modal */}
      {showModal && selectedCourse && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 border-2 border-black">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Course Details</h3>
              <button
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700 text-2xl"
              >
                ×
              </button>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white">
                  {selectedCourse.department}
                </span>
                <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                  {selectedCourse.batch}
                </span>
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4">{selectedCourse.title}</h4>
              <div className="space-y-3">
                <div className="flex items-center text-gray-700">
                  <FiUser className="mr-3 text-yellow-500" />
                  <span className="font-medium">{selectedCourse.trainer}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiMapPin className="mr-3 text-green-500" />
                  <span className="font-medium">{selectedCourse.college}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiAward className="mr-3 text-green-500" />
                  <span className="font-medium">{selectedCourse.department}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiUsers className="mr-3 text-purple-500" />
                  <span className="font-medium">{selectedCourse.batch}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiBookOpen className="mr-3 text-blue-500" />
                  <span className="font-medium">Active Course</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UploadCourseWithTrainer; 