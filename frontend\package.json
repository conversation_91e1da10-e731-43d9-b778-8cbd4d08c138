{"name": "cms-frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "framer-motion": "^12.23.12", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.2", "react-hot-toast": "^2.4.0", "react-icons": "^4.12.0", "react-redux": "^8.0.5", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "redux": "^4.2.1", "styled-components": "^6.1.19", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "tailwindcss": "^3.2.7"}}