import React, { useState } from 'react';
import { 
  <PERSON>Home, 
  FiUsers, 
  FiUser, 
  FiAward, 
  FiCalendar,
  FiSearch,
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiEye,
  FiX,
  FiSave,
  FiRefreshCw,
  FiBarChart2,
  FiCheckCircle,
  FiXCircle,
  FiBookOpen,
  FiMapPin
} from 'react-icons/fi';

const collegeOptions = ['ABC College', 'XYZ Institute'];
const batchOptions = ['Batch A – 2024', 'Batch B – 2025'];
const trainerOptions = ['Trainer A', 'Trainer B'];
const departmentOptions = ['CS', 'ECE', 'MECH'];

const mockReports = [
  {
    id: '101',
    name: '<PERSON>',
    attendance: '95%',
    coursesCompleted: '3/3',
    certificateIssued: true,
    batch: 'Batch A – 2024',
    trainer: 'Trainer A',
    college: 'ABC College',
    department: 'CS',
  },
  {
    id: '102',
    name: '<PERSON>',
    attendance: '82%',
    coursesCompleted: '2/3',
    certificateIssued: false,
    batch: 'Batch A – 2024',
    trainer: 'Trainer <PERSON>',
    college: 'ABC College',
    department: 'CS',
  },
];

const ViewBatchWiseReports = () => {
  const [college, setCollege] = useState('');
  const [batch, setBatch] = useState('');
  const [trainer, setTrainer] = useState('');
  const [department, setDepartment] = useState('');
  const [dateRange, setDateRange] = useState('');
  const [search, setSearch] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  const filteredReports = mockReports.filter(r => {
    return (
      (!college || r.college === college) &&
      (!batch || r.batch === batch) &&
      (!trainer || r.trainer === trainer) &&
      (!department || r.department === department) &&
      (!search || r.name.toLowerCase().includes(search.toLowerCase()) || r.id.includes(search))
    );
  });

  const openStudentModal = (student) => {
    setSelectedStudent(student);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedStudent(null);
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const handleReset = () => {
    setCollege('');
    setBatch('');
    setTrainer('');
    setDepartment('');
    setDateRange('');
    setSearch('');
  };

  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">View Batch-wise Reports</h1>
            <p className="text-gray-700 mt-1">Generate and view comprehensive batch reports</p>
          </div>
          <button 
            onClick={handleEditToggle}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
              isEditing 
                ? "bg-red-600 text-white hover:bg-red-700" 
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
          >
            {isEditing ? <FiX size={16} /> : <FiPlus size={16} />}
            {isEditing ? "Cancel" : "Edit Mode"}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Filter Form */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Filter Reports</h2>
              <form className="space-y-6">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiHome className="mr-2 text-red-500" />
                    Select College
                  </label>
                  <select 
                    value={college} 
                    onChange={e => setCollege(e.target.value)} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select College</option>
                    {collegeOptions.map(c => <option key={c} value={c}>{c}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiUsers className="mr-2 text-blue-500" />
                    Select Batch
                  </label>
                  <select 
                    value={batch} 
                    onChange={e => setBatch(e.target.value)} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Batch</option>
                    {batchOptions.map(b => <option key={b} value={b}>{b}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiUser className="mr-2 text-yellow-500" />
                    Select Trainer
                  </label>
                  <select 
                    value={trainer} 
                    onChange={e => setTrainer(e.target.value)} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Trainer</option>
                    {trainerOptions.map(t => <option key={t} value={t}>{t}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiAward className="mr-2 text-green-500" />
                    Select Department
                  </label>
                  <select 
                    value={department} 
                    onChange={e => setDepartment(e.target.value)} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Department</option>
                    {departmentOptions.map(dep => <option key={dep} value={dep}>{dep}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiCalendar className="mr-2 text-purple-500" />
                    Date Range
                  </label>
                  <input 
                    value={dateRange} 
                    onChange={e => setDateRange(e.target.value)} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="YYYY-MM-DD to YYYY-MM-DD" 
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiSearch className="mr-2 text-blue-500" />
                    Search
                  </label>
                  <input 
                    value={search} 
                    onChange={e => setSearch(e.target.value)} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="Search by Name or ID" 
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <button 
                    type="button" 
                    onClick={handleReset} 
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiRefreshCw className="mr-2" />
                    Reset Filters
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Right Panel - Reports Display */}
          <div className="lg:col-span-2">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
              <div className="border-b border-gray-200">
                <div className="flex space-x-8 px-6">
                  <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-blue-600 font-medium text-sm text-blue-600">
                    <FiBarChart2 size={16} />
                    Batch Reports
                  </button>
                </div>
              </div>

              <div className="p-6">
                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-gray-50 rounded-xl p-4 text-center border-2 border-black">
                    <div className="text-2xl font-bold text-gray-900 mb-1">{filteredReports.length}</div>
                    <div className="text-gray-700 text-sm font-semibold">Total Students</div>
                  </div>
                  <div className="bg-gray-50 rounded-xl p-4 text-center border-2 border-black">
                    <div className="text-lg font-bold text-gray-900 mb-1">{trainer || '-'}</div>
                    <div className="text-gray-700 text-sm font-semibold">Assigned Trainer</div>
                  </div>
                  <div className="bg-gray-50 rounded-xl p-4 text-center border-2 border-black">
                    <div className="text-lg font-bold text-gray-900 mb-1">{college || '-'}</div>
                    <div className="text-gray-700 text-sm font-semibold">College Name</div>
                  </div>
                  <div className="bg-gray-50 rounded-xl p-4 text-center border-2 border-black">
                    <div className="text-lg font-bold text-gray-900 mb-1">--%</div>
                    <div className="text-gray-700 text-sm font-semibold">Avg Attendance</div>
                  </div>
                </div>

                {/* Student Reports */}
                <div className="space-y-4">
                  {filteredReports.length === 0 ? (
                    <div className="text-center py-12">
                      <FiBarChart2 className="text-6xl text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500 text-lg">No reports found</p>
                      <p className="text-gray-400 text-sm mt-2">Try adjusting your filters</p>
                    </div>
                  ) : (
                    filteredReports.map((report) => (
                      <div key={report.id} className="bg-gray-50 rounded-xl p-6 border-2 border-black hover:shadow-lg transition-all duration-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-4">
                              <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white shadow-lg">
                                {report.batch}
                              </span>
                              <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                                {report.department}
                              </span>
                            </div>
                            <h4 className="text-xl font-bold text-gray-900 mb-3">{report.name}</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiUser className="mr-3 text-yellow-500" />
                                <span className="font-medium">ID: {report.id}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiMapPin className="mr-3 text-green-500" />
                                <span className="font-medium">{report.college}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiBarChart2 className="mr-3 text-blue-500" />
                                <span className="font-medium">Attendance: {report.attendance}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiBookOpen className="mr-3 text-purple-500" />
                                <span className="font-medium">Courses: {report.coursesCompleted}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black md:col-span-2">
                                {report.certificateIssued ? (
                                  <FiCheckCircle className="mr-3 text-green-500" />
                                ) : (
                                  <FiXCircle className="mr-3 text-red-500" />
                                )}
                                <span className="font-medium">
                                  Certificate: {report.certificateIssued ? 'Issued' : 'Pending'}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <button
                              onClick={() => openStudentModal(report)}
                              className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors border-2 border-blue-600"
                            >
                              <FiEye className="text-lg" />
                            </button>
                            <button
                              className="bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors border-2 border-green-600"
                            >
                              <FiEdit2 className="text-lg" />
                            </button>
                            <button
                              className="bg-red-600 text-white p-2 rounded-lg hover:bg-red-700 transition-colors border-2 border-red-600"
                            >
                              <FiTrash2 className="text-lg" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Student Report Details Modal */}
      {showModal && selectedStudent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 border-2 border-black">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Student Report Details</h3>
              <button
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700 text-2xl"
              >
                ×
              </button>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white">
                  {selectedStudent.batch}
                </span>
                <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                  {selectedStudent.department}
                </span>
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4">{selectedStudent.name}</h4>
              <div className="space-y-3">
                <div className="flex items-center text-gray-700">
                  <FiUser className="mr-3 text-yellow-500" />
                  <span className="font-medium">ID: {selectedStudent.id}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiMapPin className="mr-3 text-green-500" />
                  <span className="font-medium">{selectedStudent.college}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiBarChart2 className="mr-3 text-blue-500" />
                  <span className="font-medium">Attendance: {selectedStudent.attendance}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiBookOpen className="mr-3 text-purple-500" />
                  <span className="font-medium">Courses: {selectedStudent.coursesCompleted}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  {selectedStudent.certificateIssued ? (
                    <FiCheckCircle className="mr-3 text-green-500" />
                  ) : (
                    <FiXCircle className="mr-3 text-red-500" />
                  )}
                  <span className="font-medium">
                    Certificate: {selectedStudent.certificateIssued ? 'Issued' : 'Pending'}
                  </span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiUser className="mr-3 text-orange-500" />
                  <span className="font-medium">Trainer: {selectedStudent.trainer}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ViewBatchWiseReports; 