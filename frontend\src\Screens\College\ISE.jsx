import React, { useState } from "react";
import CollegeLayout from "../../components/Layout/CollegeLayout";
import { FiArrowLeft } from "react-icons/fi";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";

const iseBatches = [
  { batch: "2020", count: 30 },
  { batch: "2021", count: 32 },
  { batch: "2022", count: 29 },
  { batch: "2023", count: 34 },
];

function ISE(props) {
  const navigate = useNavigate();
  const [selectedBatch, setSelectedBatch] = useState(iseBatches[0].batch);
  const selected = iseBatches.find(b => b.batch === selectedBatch);
  const [showSection, setShowSection] = useState(null); // 'studentList', 'results', 'attendance', 'marks'

  // Dummy data by batch (replace with ISE-specific names if needed)
  const studentsByBatch = {
    '2020': [
      { name: "<PERSON><PERSON><PERSON>", usn: "ISE201", email: "<EMAIL>" },
      { name: "<PERSON><PERSON> <PERSON>", usn: "ISE202", email: "<EMAIL>" },
    ],
    '2021': [
      { name: "<PERSON><PERSON><PERSON>", usn: "ISE211", email: "<EMAIL>" },
      { name: "Asha Menon", usn: "ISE212", email: "<EMAIL>" },
    ],
    '2022': [
      { name: "Vivek S", usn: "ISE221", email: "<EMAIL>" },
      { name: "Nisha K", usn: "ISE222", email: "<EMAIL>" },
    ],
    '2023': [
      { name: "Arjun R", usn: "ISE231", email: "<EMAIL>" },
      { name: "Pooja L", usn: "ISE232", email: "<EMAIL>" },
    ],
  };
  // Results: only pass/fail for each subject
  const resultsByBatch = {
    '2020': [
      { student: "Rohan Das", autocad: "Pass", revitArc: "Pass", revitStruct: "Pass", revitMEP: "Pass", staad: "Pass", naviworks: "Pass", msp: "Pass", primavera: "Pass" },
      { student: "Meera Nair", autocad: "Pass", revitArc: "Fail", revitStruct: "Pass", revitMEP: "Pass", staad: "Pass", naviworks: "Pass", msp: "Fail", primavera: "Pass" },
    ],
    '2021': [
      { student: "Siddharth Jain", autocad: "Pass", revitArc: "Pass", revitStruct: "Pass", revitMEP: "Pass", staad: "Pass", naviworks: "Pass", msp: "Pass", primavera: "Pass" },
      { student: "Asha Menon", autocad: "Pass", revitArc: "Pass", revitStruct: "Pass", revitMEP: "Pass", staad: "Pass", naviworks: "Pass", msp: "Pass", primavera: "Pass" },
    ],
    '2022': [
      { student: "Vivek S", autocad: "Pass", revitArc: "Pass", revitStruct: "Pass", revitMEP: "Pass", staad: "Pass", naviworks: "Pass", msp: "Pass", primavera: "Pass" },
      { student: "Nisha K", autocad: "Pass", revitArc: "Pass", revitStruct: "Fail", revitMEP: "Pass", staad: "Pass", naviworks: "Pass", msp: "Pass", primavera: "Pass" },
    ],
    '2023': [
      { student: "Arjun R", autocad: "Pass", revitArc: "Pass", revitStruct: "Pass", revitMEP: "Pass", staad: "Pass", naviworks: "Pass", msp: "Pass", primavera: "Pass" },
      { student: "Pooja L", autocad: "Pass", revitArc: "Pass", revitStruct: "Pass", revitMEP: "Pass", staad: "Pass", naviworks: "Pass", msp: "Pass", primavera: "Pass" },
    ],
  };
  const attendanceByBatch = {
    '2020': [
      { student: "Rohan Das", subject: "Autocad", total: 30, attended: 28, percentage: "93%" },
      { student: "Rohan Das", subject: "Revit Arc", total: 30, attended: 29, percentage: "97%" },
      { student: "Rohan Das", subject: "Revit Structure", total: 30, attended: 27, percentage: "90%" },
      { student: "Rohan Das", subject: "Revit MEP", total: 30, attended: 28, percentage: "93%" },
      { student: "Meera Nair", subject: "Staad Pro", total: 30, attended: 30, percentage: "100%" },
      { student: "Meera Nair", subject: "Naviworks", total: 30, attended: 29, percentage: "97%" },
      { student: "Meera Nair", subject: "MSP", total: 30, attended: 28, percentage: "93%" },
      { student: "Meera Nair", subject: "Primavera", total: 30, attended: 27, percentage: "90%" },
    ],
    '2021': [
      { student: "Siddharth Jain", subject: "Autocad", total: 31, attended: 30, percentage: "97%" },
      { student: "Siddharth Jain", subject: "Revit Arc", total: 31, attended: 29, percentage: "94%" },
      { student: "Siddharth Jain", subject: "Revit Structure", total: 31, attended: 30, percentage: "97%" },
      { student: "Siddharth Jain", subject: "Revit MEP", total: 31, attended: 28, percentage: "90%" },
      { student: "Asha Menon", subject: "Staad Pro", total: 31, attended: 31, percentage: "100%" },
      { student: "Asha Menon", subject: "Naviworks", total: 31, attended: 30, percentage: "97%" },
      { student: "Asha Menon", subject: "MSP", total: 31, attended: 29, percentage: "94%" },
      { student: "Asha Menon", subject: "Primavera", total: 31, attended: 28, percentage: "90%" },
    ],
    '2022': [
      { student: "Vivek S", subject: "Autocad", total: 29, attended: 27, percentage: "93%" },
      { student: "Vivek S", subject: "Revit Arc", total: 29, attended: 28, percentage: "97%" },
      { student: "Vivek S", subject: "Revit Structure", total: 29, attended: 27, percentage: "93%" },
      { student: "Vivek S", subject: "Revit MEP", total: 29, attended: 26, percentage: "90%" },
      { student: "Nisha K", subject: "Staad Pro", total: 29, attended: 29, percentage: "100%" },
      { student: "Nisha K", subject: "Naviworks", total: 29, attended: 28, percentage: "97%" },
      { student: "Nisha K", subject: "MSP", total: 29, attended: 27, percentage: "93%" },
      { student: "Nisha K", subject: "Primavera", total: 29, attended: 26, percentage: "90%" },
    ],
    '2023': [
      { student: "Arjun R", subject: "Autocad", total: 30, attended: 29, percentage: "97%" },
      { student: "Arjun R", subject: "Revit Arc", total: 31, attended: 30, percentage: "97%" },
      { student: "Arjun R", subject: "Revit Structure", total: 30, attended: 29, percentage: "97%" },
      { student: "Arjun R", subject: "Revit MEP", total: 31, attended: 30, percentage: "97%" },
      { student: "Pooja L", subject: "Staad Pro", total: 30, attended: 30, percentage: "100%" },
      { student: "Pooja L", subject: "Naviworks", total: 31, attended: 30, percentage: "97%" },
      { student: "Pooja L", subject: "MSP", total: 30, attended: 29, percentage: "97%" },
      { student: "Pooja L", subject: "Primavera", total: 31, attended: 30, percentage: "97%" },
    ],
  };
  // Marks: same as previous results table (marks for each subject)
  const marksByBatch = {
    '2020': [
      { student: "Rohan Das", autocad: 90, revitArc: 85, revitStruct: 88, revitMEP: 87, staad: 92, naviworks: 80, msp: 78, primavera: 85 },
      { student: "Meera Nair", autocad: 88, revitArc: 80, revitStruct: 82, revitMEP: 85, staad: 90, naviworks: 75, msp: 80, primavera: 82 },
    ],
    '2021': [
      { student: "Siddharth Jain", autocad: 85, revitArc: 82, revitStruct: 80, revitMEP: 83, staad: 88, naviworks: 77, msp: 79, primavera: 80 },
      { student: "Asha Menon", autocad: 92, revitArc: 88, revitStruct: 90, revitMEP: 89, staad: 94, naviworks: 82, msp: 85, primavera: 88 },
    ],
    '2022': [
      { student: "Vivek S", autocad: 87, revitArc: 84, revitStruct: 86, revitMEP: 85, staad: 89, naviworks: 79, msp: 81, primavera: 83 },
      { student: "Nisha K", autocad: 90, revitArc: 86, revitStruct: 88, revitMEP: 87, staad: 92, naviworks: 80, msp: 84, primavera: 86 },
    ],
    '2023': [
      { student: "Arjun R", autocad: 91, revitArc: 89, revitStruct: 90, revitMEP: 88, staad: 93, naviworks: 85, msp: 87, primavera: 90 },
      { student: "Pooja L", autocad: 88, revitArc: 85, revitStruct: 87, revitMEP: 86, staad: 90, naviworks: 80, msp: 82, primavera: 85 },
    ],
  };

  const students = studentsByBatch[selectedBatch] || [];
  const results = resultsByBatch[selectedBatch] || [];
  const attendance = attendanceByBatch[selectedBatch] || [];
  const marks = marksByBatch[selectedBatch] || [];

  return (
    <CollegeLayout>
      <nav className="w-full shadow flex items-center justify-between px-6 py-4 mb-4 bg-gradient-to-r" style={{ background: 'linear-gradient(90deg,rgb(35, 65, 75) 0%,rgb(35, 65, 75) 100%)' }}>
        <div className="flex items-center gap-3">
          <button onClick={() => navigate(-1)} className="text-white hover:bg-white/10 rounded-full p-2 mr-2" title="Back">
            <FiArrowLeft size={22} />
          </button>
        </div>
        <div>
          <img src="/images/Medini logo White.png" alt="Logo" className="h-10 rounded" />
        </div>
      </nav>
      <motion.div className="w-full min-h-screen bg-white p-8"
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7 }}
      >
        <h1 className="text-3xl font-bold text-gray-800 mb-6">ISE</h1>
        <label className="block mb-2 text-lg font-semibold text-gray-700">Choose Batch</label>
        <select
          className="w-full max-w-md border border-gray-300 rounded px-4 py-2 mb-6 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-white text-gray-800"
          value={selectedBatch}
          onChange={e => setSelectedBatch(e.target.value)}
          style={{ backgroundColor: 'white' }}
        >
          {iseBatches.map(b => (
            <option key={b.batch} value={b.batch} style={{ backgroundColor: 'white', color: '#1f2937' }}>{b.batch}</option>
          ))}
        </select>
        <div className="text-lg text-gray-800 font-medium mb-8">
          Number of Students: <span className="text-blue-700 font-bold">{selected.count}</span>
        </div>
        {showSection === null && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Student List Section */}
            <div className="bg-blue-50 rounded-xl shadow p-6 border border-blue-100 cursor-pointer transition-all" onClick={() => setShowSection('studentList')}>
              <h2 className="text-xl font-bold text-blue-800 mb-4">Student List</h2>
              <div className="text-gray-600">(Click to view student list)</div>
            </div>
            {/* Results Section */}
            <div className="bg-green-50 rounded-xl shadow p-6 border border-green-100 cursor-pointer transition-all" onClick={() => setShowSection('results')}>
              <h2 className="text-xl font-bold text-green-800 mb-4">Results</h2>
              <div className="text-gray-600">(Click to view results)</div>
            </div>
            {/* Attendance Section */}
            <div className="bg-purple-50 rounded-xl shadow p-6 border border-purple-100 cursor-pointer transition-all" onClick={() => setShowSection('attendance')}>
              <h2 className="text-xl font-bold text-purple-800 mb-4">Attendance</h2>
              <div className="text-gray-600">(Click to view attendance)</div>
            </div>
            {/* Marks Section */}
            <div className="bg-orange-50 rounded-xl shadow p-6 border border-orange-100 cursor-pointer transition-all" onClick={() => setShowSection('marks')}>
              <h2 className="text-xl font-bold text-orange-800 mb-4">Marks</h2>
              <div className="text-gray-600">(Click to view marks)</div>
            </div>
          </div>
        )}
        {showSection === 'studentList' && (
          <div className="bg-white rounded-xl shadow p-8 border border-blue-100">
            <div className="overflow-x-auto w-full">
              <button onClick={() => setShowSection(null)} className="mb-6 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition">Back</button>
              <h2 className="text-2xl font-bold text-blue-800 mb-6">Student List</h2>
              <table className="min-w-full border border-gray-200 rounded-lg overflow-hidden">
                <thead className="bg-blue-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-700">Name</th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-700">USN</th>
                    <th className="px-6 py-3 text-left text-sm font-semibold text-gray-700">Email</th>
                  </tr>
                </thead>
                <tbody>
                  {students.map((student, idx) => (
                    <tr key={student.usn} className="border-b last:border-b-0 hover:bg-blue-50/40 transition">
                      <td className="px-6 py-3 text-gray-800 font-medium">{student.name}</td>
                      <td className="px-6 py-3 text-gray-800">{student.usn}</td>
                      <td className="px-6 py-3 text-gray-800">{student.email}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
        {showSection === 'results' && (
          <div className="bg-white rounded-xl shadow p-8 border border-green-100">
            <div className="overflow-x-auto w-full">
              <button onClick={() => setShowSection(null)} className="mb-6 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition">Back</button>
              <h2 className="text-2xl font-bold text-green-800 mb-6">Results</h2>
              <table className="min-w-full border border-gray-200 rounded-lg overflow-hidden text-xs md:text-sm">
                <thead className="bg-green-50">
                  <tr>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Student</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Autocad</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Revit Arc</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Revit Structure</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Revit MEP</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Staad Pro</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Naviworks</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">MSP</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Primavera</th>
                  </tr>
                </thead>
                <tbody>
                  {results.map((r, idx) => (
                    <tr key={r.student} className="border-b last:border-b-0 hover:bg-green-50/40 transition">
                      <td className="px-4 py-3 text-gray-800 font-medium">{r.student}</td>
                      <td className="px-4 py-3 text-gray-800">{r.autocad}</td>
                      <td className="px-4 py-3 text-gray-800">{r.revitArc}</td>
                      <td className="px-4 py-3 text-gray-800">{r.revitStruct}</td>
                      <td className="px-4 py-3 text-gray-800">{r.revitMEP}</td>
                      <td className="px-4 py-3 text-gray-800">{r.staad}</td>
                      <td className="px-4 py-3 text-gray-800">{r.naviworks}</td>
                      <td className="px-4 py-3 text-gray-800">{r.msp}</td>
                      <td className="px-4 py-3 text-gray-800">{r.primavera}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
        {showSection === 'attendance' && (
          <div className="bg-white rounded-xl shadow p-8 border border-purple-100">
            <div className="overflow-x-auto w-full">
              <button onClick={() => setShowSection(null)} className="mb-6 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition">Back</button>
              <h2 className="text-2xl font-bold text-purple-800 mb-6">Attendance</h2>
              <table className="min-w-full border border-gray-200 rounded-lg overflow-hidden text-xs md:text-sm">
                <thead className="bg-purple-50">
                  <tr>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Student</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Subject</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Total Classes</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Attended</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Percentage</th>
                  </tr>
                </thead>
                <tbody>
                  {attendance.map((a, idx) => (
                    <tr key={a.student + a.subject} className="border-b last:border-b-0 hover:bg-purple-50/40 transition">
                      <td className="px-4 py-3 text-gray-800 font-medium">{a.student}</td>
                      <td className="px-4 py-3 text-gray-800">{a.subject}</td>
                      <td className="px-4 py-3 text-gray-800">{a.total}</td>
                      <td className="px-4 py-3 text-gray-800">{a.attended}</td>
                      <td className="px-4 py-3 text-gray-800">{a.percentage}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
        {showSection === 'marks' && (
          <div className="bg-white rounded-xl shadow p-8 border border-orange-100">
            <div className="overflow-x-auto w-full">
              <button onClick={() => setShowSection(null)} className="mb-6 px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 transition">Back</button>
              <h2 className="text-2xl font-bold text-orange-800 mb-6">Marks</h2>
              <table className="min-w-full border border-gray-200 rounded-lg overflow-hidden text-xs md:text-sm">
                <thead className="bg-orange-50">
                  <tr>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Student</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Autocad</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Revit Arc</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Revit Structure</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Revit MEP</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Staad Pro</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Naviworks</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">MSP</th>
                    <th className="px-4 py-3 text-left font-semibold text-gray-700">Primavera</th>
                  </tr>
                </thead>
                <tbody>
                  {marks.map((m, idx) => (
                    <tr key={m.student} className="border-b last:border-b-0 hover:bg-orange-50/40 transition">
                      <td className="px-4 py-3 text-gray-800 font-medium">{m.student}</td>
                      <td className="px-4 py-3 text-gray-800">{m.autocad}</td>
                      <td className="px-4 py-3 text-gray-800">{m.revitArc}</td>
                      <td className="px-4 py-3 text-gray-800">{m.revitStruct}</td>
                      <td className="px-4 py-3 text-gray-800">{m.revitMEP}</td>
                      <td className="px-4 py-3 text-gray-800">{m.staad}</td>
                      <td className="px-4 py-3 text-gray-800">{m.naviworks}</td>
                      <td className="px-4 py-3 text-gray-800">{m.msp}</td>
                      <td className="px-4 py-3 text-gray-800">{m.primavera}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </motion.div>
    </CollegeLayout>
  );
};

export default ISE; 