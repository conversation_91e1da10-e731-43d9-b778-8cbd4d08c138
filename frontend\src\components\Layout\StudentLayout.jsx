import React, { useState, useEffect } from 'react';
import { FiMenu, FiX, FiHome, FiBook, FiCalendar, FiClock, FiUser, FiSettings, FiLogOut, FiAward, FiFileText } from 'react-icons/fi';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import { theme } from '../../theme';
import styled from 'styled-components';

// Reuse the styled components from AdminLayout with some adjustments
const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: ${theme.colors.background};
  color: ${theme.colors.text.primary};
`;

const Sidebar = styled.aside`
  width: 250px;
  background-color: ${theme.colors.surface};
  border-right: 1px solid ${theme.colors.border};
  position: fixed;
  top: 0;
  left: ${({ $isOpen }) => ($isOpen ? '0' : '-250px')};
  bottom: 0;
  z-index: 50;
  transition: left 0.3s ease;
  overflow-y: auto;
  
  @media (min-width: ${theme.breakpoints.tablet}) {
    left: 0;
  }
`;

const SidebarHeader = styled.div`
  padding: ${theme.spacing.lg};
  border-bottom: 1px solid ${theme.colors.border};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const MenuButton = styled.button`
  background: none;
  border: none;
  color: ${theme.colors.text.primary};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${theme.spacing.sm};
  border-radius: 50%;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  @media (min-width: ${theme.breakpoints.tablet}) {
    display: none;
  }
`;

const MenuList = styled.ul`
  list-style: none;
  padding: ${theme.spacing.md};
  margin: 0;
`;

const MenuItem = styled.li`
  margin-bottom: ${theme.spacing.sm};
`;

const MenuLink = styled(Link)`
  display: flex;
  align-items: center;
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  color: ${({ $isActive }) => $isActive ? theme.colors.secondary : theme.colors.text.primary};
  text-decoration: none;
  border-radius: ${theme.borderRadius.md};
  background-color: ${({ $isActive }) => $isActive ? 'rgba(255, 255, 255, 0.1)' : 'transparent'};
  transition: all 0.2s ease;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  svg {
    margin-right: ${theme.spacing.sm};
  }
`;

const MainContent = styled.main`
  flex: 1;
  margin-left: 0;
  padding: ${theme.spacing.lg};
  transition: margin-left 0.3s ease;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  
  @media (min-width: ${theme.breakpoints.tablet}) {
    margin-left: 250px;
    width: calc(100% - 250px);
  }
`;

const StudentLayout = ({ children }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const navigate = useNavigate();
  const location = useLocation();

  // Student-specific menu items
  const menuItems = [
    { icon: <FiHome size={20} />, label: 'Dashboard', path: '/student' },
    { icon: <FiBook size={20} />, label: 'My Courses', path: '/student/courses' },
    { icon: <FiCalendar size={20} />, label: 'Schedule', path: '/student/schedule' },


    { icon: <FiAward size={20} />, label: 'Certificate', path: '/student/certificate' },
    { icon: <FiUser size={20} />, label: 'Profile', path: '/student/profile' },
  ];

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (!mobile) {
        setIsSidebarOpen(true);
      } else {
        setIsSidebarOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initial check
    
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLogout = () => {
    // Clear any authentication tokens or user data
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    // Redirect to login page
    navigate('/login');
    
    // Close sidebar on mobile after navigation
    if (isMobile) {
      setIsSidebarOpen(false);
    }
  };

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isMobile && isSidebarOpen && !event.target.closest('aside')) {
        setIsSidebarOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, isSidebarOpen]);

  return (
    <LayoutContainer>
      {/* Mobile Sidebar Overlay */}
      {isMobile && isSidebarOpen && (
        <div 
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            zIndex: 40,
          }}
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <Sidebar $isOpen={isSidebarOpen}>
        <SidebarHeader>
          <img src="/images/Medini logo White.png" alt="Logo" style={{ height: 40, marginRight: 12, borderRadius: 8 }} />
          
          <MenuButton onClick={() => setIsSidebarOpen(false)}>
            <FiX size={24} />
          </MenuButton>
        </SidebarHeader>

        <nav>
          <MenuList>
            {menuItems.map((item) => (
              <MenuItem key={item.path}>
                <MenuLink
                  to={item.path}
                  $isActive={location.pathname === item.path}
                  onClick={() => isMobile && setIsSidebarOpen(false)}
                >
                  {item.icon}
                  {item.label}
                </MenuLink>
              </MenuItem>
            ))}
          </MenuList>
        </nav>

        <div style={{
          padding: theme.spacing.md,
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          borderTop: `1px solid ${theme.colors.border}`,
        }}>
          <MenuLink 
            as="button"
            onClick={handleLogout}
            style={{
              width: '100%',
              textAlign: 'left',
              cursor: 'pointer',
            }}
          >
            <FiLogOut />
            Logout
          </MenuLink>
        </div>
      </Sidebar>

      {/* Main Content */}
      <MainContent>
        {/* Page Content */}
        <div style={{
          backgroundColor: theme.colors.background,
          borderRadius: theme.borderRadius.lg,
          padding: isMobile ? theme.spacing.md : theme.spacing.lg,
          minHeight: 'calc(100vh - 120px)',
        }}>
          {children}
        </div>
      </MainContent>
    </LayoutContainer>
  );
};

export default StudentLayout;
