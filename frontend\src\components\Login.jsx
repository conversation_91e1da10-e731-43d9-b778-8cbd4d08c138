import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { FiLogIn, FiCalendar, FiShield } from "react-icons/fi";
import { useNavigate } from "react-router-dom";
import toast, { Toaster } from "react-hot-toast";
import { authService } from "../services/mockData";
import { theme } from "../theme";

const Login = () => {
  const navigate = useNavigate();
  const [selected, setSelected] = useState("Student");
  const { register, handleSubmit } = useForm();
  const onSubmit = (data) => {
    if (data.loginid !== "" && data.password !== "") {
      authService.login(data.loginid, data.password, selected)
        .then((response) => {
          if (selected === 'College') {
            navigate('/college', {
              state: { type: selected, loginid: response.loginid },
            });
          } else if (selected === 'Admin') {
            navigate('/admin/create-batch', {
              state: { type: selected, loginid: response.loginid },
            });
          } else {
            navigate(`/${selected.toLowerCase()}`, {
              state: { type: selected, loginid: response.loginid },
            });
          }
        })
        .catch((error) => {
          toast.dismiss();
          console.error(error);
          toast.error(error.message);
        });
    } else {
    }
  };
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: theme.colors.background,
      color: theme.colors.text.primary,
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      padding: isMobile ? '1rem' : '2rem',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      textAlign: 'center',
    }}>
      {/* Header */}
      <div style={{
        marginBottom: theme.spacing.xl,
        width: '100%',
        maxWidth: '400px',
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: theme.spacing.md,
        }}>
          <span style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: theme.colors.primary,
            backgroundColor: theme.colors.secondary,
            padding: '0.5rem 1rem',
            borderRadius: theme.borderRadius.full,
          }}>start</span>
          <h1 style={{
            margin: 0,
            marginLeft: theme.spacing.sm,
            fontSize: '1.8rem',
            fontWeight: '600',
          }}>Welcome</h1>
        </div>

        <div style={{
          display: 'flex',
          justifyContent: 'center',
          gap: theme.spacing.lg,
          marginBottom: theme.spacing.xl,
        }}>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}>
            <div style={{
              width: '50px',
              height: '50px',
              backgroundColor: theme.colors.surface,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: theme.spacing.sm,
            }}>
              <FiCalendar size={24} color={theme.colors.text.primary} />
            </div>
            <span style={{ fontSize: '0.8rem' }}>Calendar</span>
          </div>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}>
            <div style={{
              width: '50px',
              height: '50px',
              backgroundColor: theme.colors.surface,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: theme.spacing.sm,
            }}>
              <FiShield size={24} color={theme.colors.text.primary} />
            </div>
            <span style={{ fontSize: '0.8rem' }}>Secure</span>
          </div>
        </div>

        <h2 style={{
          fontSize: '2rem',
          fontWeight: '700',
          marginBottom: theme.spacing.sm,
          lineHeight: '1.2',
        }}>Your Educational Hub</h2>
        
        <p style={{
          color: theme.colors.text.secondary,
          marginBottom: theme.spacing.xl,
          fontSize: '1rem',
          lineHeight: '1.5',
        }}>Manage your institution efficiently.</p>
      </div>

      {/* Login Form */}
      <div style={{
        width: '100%',
        maxWidth: '400px',
        backgroundColor: theme.colors.surface,
        borderRadius: theme.borderRadius.lg,
        padding: theme.spacing.lg,
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: theme.spacing.lg,
          borderBottom: `1px solid ${theme.colors.border}`,
          paddingBottom: theme.spacing.sm,
        }}>
          {['Student', 'Faculty', 'Admin', 'College'].map((role) => (
            <button
              key={role}
              onClick={() => setSelected(role)}
              style={{
                flex: 1,
                padding: '0.75rem',
                backgroundColor: 'transparent',
                color: selected === role ? theme.colors.secondary : theme.colors.text.secondary,
                border: 'none',
                borderBottom: selected === role ? `2px solid ${theme.colors.secondary}` : 'none',
                cursor: 'pointer',
                fontWeight: '500',
                transition: 'all 0.2s ease',
              }}
            >
              {role}
            </button>
          ))}
        </div>

        <form onSubmit={handleSubmit(onSubmit)} style={{
          display: 'flex',
          flexDirection: 'column',
          gap: theme.spacing.md,
        }}>
          <div>
            <input
              type="text"
              id="loginid"
              placeholder={`${selected} ID`}
              {...register("loginid", { required: true })}
              autoComplete="username"
              style={{
                width: '100%',
                padding: '0.75rem 1rem',
                backgroundColor: theme.colors.background,
                border: `1px solid ${theme.colors.border}`,
                borderRadius: theme.borderRadius.md,
                color: theme.colors.text.primary,
                fontSize: '1rem',
                outline: 'none',
                transition: 'border-color 0.2s ease',
              }}
            />
          </div>
          <div>
            <input
              type="password"
              id="password"
              placeholder="Password"
              {...register("password", { required: true })}
              autoComplete="current-password"
              style={{
                width: '100%',
                padding: '0.75rem 1rem',
                backgroundColor: theme.colors.background,
                border: `1px solid ${theme.colors.border}`,
                borderRadius: theme.borderRadius.md,
                color: theme.colors.text.primary,
                fontSize: '1rem',
                outline: 'none',
                transition: 'border-color 0.2s ease',
              }}
            />
          </div>
          <button
            type="submit"
            style={{
              backgroundColor: theme.colors.primary,
              color: theme.colors.secondary,
              padding: '0.75rem',
              borderRadius: theme.borderRadius.full,
              border: 'none',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '0.5rem',
              marginTop: theme.spacing.md,
              transition: 'opacity 0.2s ease',
            }}
            onMouseOver={(e) => e.currentTarget.style.opacity = '0.9'}
            onMouseOut={(e) => e.currentTarget.style.opacity = '1'}
          >
            Get Started
            <FiLogIn size={18} />
          </button>
        </form>
      </div>

      <Toaster position="bottom-center" toastOptions={{
        style: {
          background: theme.colors.surface,
          color: theme.colors.text.primary,
          border: `1px solid ${theme.colors.border}`,
        },
      }} />
    </div>
  );
};

export default Login;
