import React, { useState } from 'react';
import { 
  FiUser, 
  FiHome, 
  FiAward, 
  FiSearch,
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiEye,
  FiX,
  FiSave,
  FiRefreshCw,
  FiBarChart2,
  FiMail,
  FiCalendar,
  FiCheckCircle,
  FiXCircle,
  FiBookOpen,
  FiMapPin,
  FiShield
} from 'react-icons/fi';

const roleOptions = ['Student', 'Trainer', 'Faculty', 'Admin'];
const collegeOptions = ['ABC College', 'XYZ Institute'];
const departmentOptions = ['CS', 'ECE', 'MECH'];

const mockUsers = [
  {
    id: '101',
    name: '<PERSON>',
    role: 'Student',
    college: 'ABC College',
    email: '<EMAIL>',
    lastLogin: '2025-07-25',
    status: 'Active',
    courses: ['Python', 'Math'],
    attendance: '95%',
    certificates: ['Python'],
    department: 'CS',
  },
  {
    id: '201',
    name: '<PERSON>',
    role: 'Trainer',
    college: 'XYZ Institute',
    email: '<EMAIL>',
    lastLogin: '2025-07-24',
    status: 'Inactive',
    courses: ['AI'],
    attendance: '90%',
    certificates: [],
    department: 'ECE',
  },
];

const ViewUserReports = () => {
  const [role, setRole] = useState('');
  const [college, setCollege] = useState('');
  const [department, setDepartment] = useState('');
  const [search, setSearch] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  const filteredUsers = mockUsers.filter(u => {
    return (
      (!role || u.role === role) &&
      (!college || u.college === college) &&
      (!department || u.department === department) &&
      (!search || u.name.toLowerCase().includes(search.toLowerCase()) || u.id.includes(search) || u.email.toLowerCase().includes(search.toLowerCase()))
    );
  });

  const openUserModal = (user) => {
    setSelectedUser(user);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedUser(null);
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const handleReset = () => {
    setRole('');
    setCollege('');
    setDepartment('');
    setSearch('');
  };

  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">View User Reports</h1>
            <p className="text-gray-700 mt-1">Generate and view comprehensive user reports</p>
          </div>
          <button 
            onClick={handleEditToggle}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
              isEditing 
                ? "bg-red-600 text-white hover:bg-red-700" 
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
          >
            {isEditing ? <FiX size={16} /> : <FiPlus size={16} />}
            {isEditing ? "Cancel" : "Edit Mode"}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Filter Form */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Filter Users</h2>
              <form className="space-y-6">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiUser className="mr-2 text-blue-500" />
                    User Role
                  </label>
                  <select 
                    value={role} 
                    onChange={e => setRole(e.target.value)} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Role</option>
                    {roleOptions.map(r => <option key={r} value={r}>{r}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiHome className="mr-2 text-red-500" />
                    College
                  </label>
                  <select 
                    value={college} 
                    onChange={e => setCollege(e.target.value)} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select College</option>
                    {collegeOptions.map(c => <option key={c} value={c}>{c}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiAward className="mr-2 text-green-500" />
                    Department
                  </label>
                  <select 
                    value={department} 
                    onChange={e => setDepartment(e.target.value)} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Department</option>
                    {departmentOptions.map(dep => <option key={dep} value={dep}>{dep}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiSearch className="mr-2 text-purple-500" />
                    Search
                  </label>
                  <input 
                    value={search} 
                    onChange={e => setSearch(e.target.value)} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="Search by Name, ID, or Email" 
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <button 
                    type="button" 
                    onClick={handleReset} 
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiRefreshCw className="mr-2" />
                    Reset Filters
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Right Panel - User Reports Display */}
          <div className="lg:col-span-2">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
              <div className="border-b border-gray-200">
                <div className="flex space-x-8 px-6">
                  <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-blue-600 font-medium text-sm text-blue-600">
                    <FiBarChart2 size={16} />
                    User Reports
                  </button>
                </div>
              </div>

              <div className="p-6">
                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-gray-50 rounded-xl p-4 text-center border-2 border-black">
                    <div className="text-2xl font-bold text-gray-900 mb-1">{filteredUsers.length}</div>
                    <div className="text-gray-700 text-sm font-semibold">Total Users</div>
                  </div>
                  <div className="bg-gray-50 rounded-xl p-4 text-center border-2 border-black">
                    <div className="text-lg font-bold text-gray-900 mb-1">{role || 'All'}</div>
                    <div className="text-gray-700 text-sm font-semibold">Selected Role</div>
                  </div>
                  <div className="bg-gray-50 rounded-xl p-4 text-center border-2 border-black">
                    <div className="text-lg font-bold text-gray-900 mb-1">{college || 'All'}</div>
                    <div className="text-gray-700 text-sm font-semibold">College</div>
                  </div>
                  <div className="bg-gray-50 rounded-xl p-4 text-center border-2 border-black">
                    <div className="text-lg font-bold text-gray-900 mb-1">{department || 'All'}</div>
                    <div className="text-gray-700 text-sm font-semibold">Department</div>
                  </div>
                </div>

                {/* User Reports */}
                <div className="space-y-4">
                  {filteredUsers.length === 0 ? (
                    <div className="text-center py-12">
                      <FiBarChart2 className="text-6xl text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500 text-lg">No users found</p>
                      <p className="text-gray-400 text-sm mt-2">Try adjusting your filters</p>
                    </div>
                  ) : (
                    filteredUsers.map((user) => (
                      <div key={user.id} className="bg-gray-50 rounded-xl p-6 border-2 border-black hover:shadow-lg transition-all duration-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-4">
                              <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white shadow-lg">
                                {user.role}
                              </span>
                              <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                                {user.department}
                              </span>
                              <span className={`text-sm px-3 py-1 rounded-lg border-2 ${
                                user.status === 'Active' 
                                  ? 'bg-green-100 text-green-700 border-green-200' 
                                  : 'bg-red-100 text-red-700 border-red-200'
                              }`}>
                                {user.status}
                              </span>
                            </div>
                            <h4 className="text-xl font-bold text-gray-900 mb-3">{user.name}</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiUser className="mr-3 text-yellow-500" />
                                <span className="font-medium">ID: {user.id}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiMapPin className="mr-3 text-green-500" />
                                <span className="font-medium">{user.college}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiMail className="mr-3 text-blue-500" />
                                <span className="font-medium">{user.email}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiCalendar className="mr-3 text-purple-500" />
                                <span className="font-medium">Last: {user.lastLogin}</span>
                              </div>
                              {user.role === 'Student' && (
                                <>
                                  <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                    <FiBarChart2 className="mr-3 text-blue-500" />
                                    <span className="font-medium">Attendance: {user.attendance}</span>
                                  </div>
                                  <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                    <FiBookOpen className="mr-3 text-green-500" />
                                    <span className="font-medium">Courses: {user.courses.length}</span>
                                  </div>
                                </>
                              )}
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <button
                              onClick={() => openUserModal(user)}
                              className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors border-2 border-blue-600"
                            >
                              <FiEye className="text-lg" />
                            </button>
                            <button
                              className="bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors border-2 border-green-600"
                            >
                              <FiEdit2 className="text-lg" />
                            </button>
                            <button
                              className="bg-red-600 text-white p-2 rounded-lg hover:bg-red-700 transition-colors border-2 border-red-600"
                            >
                              <FiTrash2 className="text-lg" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* User Report Details Modal */}
      {showModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 border-2 border-black">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">User Report Details</h3>
              <button
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700 text-2xl"
              >
                ×
              </button>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white">
                  {selectedUser.role}
                </span>
                <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                  {selectedUser.department}
                </span>
                <span className={`text-sm px-3 py-1 rounded-lg border-2 ${
                  selectedUser.status === 'Active' 
                    ? 'bg-green-100 text-green-700 border-green-200' 
                    : 'bg-red-100 text-red-700 border-red-200'
                }`}>
                  {selectedUser.status}
                </span>
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4">{selectedUser.name}</h4>
              <div className="space-y-3">
                <div className="flex items-center text-gray-700">
                  <FiUser className="mr-3 text-yellow-500" />
                  <span className="font-medium">ID: {selectedUser.id}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiMapPin className="mr-3 text-green-500" />
                  <span className="font-medium">{selectedUser.college}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiMail className="mr-3 text-blue-500" />
                  <span className="font-medium">{selectedUser.email}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiCalendar className="mr-3 text-purple-500" />
                  <span className="font-medium">Last Login: {selectedUser.lastLogin}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiShield className="mr-3 text-orange-500" />
                  <span className="font-medium">Status: {selectedUser.status}</span>
                </div>
                {selectedUser.role === 'Student' && (
                  <>
                    <div className="flex items-center text-gray-700">
                      <FiBarChart2 className="mr-3 text-blue-500" />
                      <span className="font-medium">Attendance: {selectedUser.attendance}</span>
                    </div>
                    <div className="flex items-center text-gray-700">
                      <FiBookOpen className="mr-3 text-green-500" />
                      <span className="font-medium">Courses: {selectedUser.courses.join(', ')}</span>
                    </div>
                    <div className="flex items-center text-gray-700">
                      {selectedUser.certificates.length > 0 ? (
                        <FiCheckCircle className="mr-3 text-green-500" />
                      ) : (
                        <FiXCircle className="mr-3 text-red-500" />
                      )}
                      <span className="font-medium">
                        Certificates: {selectedUser.certificates.length > 0 ? selectedUser.certificates.join(', ') : 'None'}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ViewUserReports; 