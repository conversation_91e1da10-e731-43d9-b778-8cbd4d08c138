import React, { useState } from "react";
import toast from "react-hot-toast";
import Heading from "../../components/Heading";
import { baseApiURL } from "../../baseUrl";
import { FiSearch } from "react-icons/fi";
import { dataService, authService } from '../../services/mockData';

const Student = () => {
  const [search, setSearch] = useState();
  const [data, setData] = useState({
    enrollmentNo: "",
    firstName: "",
    middleName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    semester: "",
    branch: "",
    gender: "",
    profile: "",
  });
  const [id, setId] = useState();

  const searchStudentHandler = (e) => {
    setId("");
    setData({
      enrollmentNo: "",
      firstName: "",
      middleName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      semester: "",
      branch: "",
      gender: "",
      profile: "",
    });
    e.preventDefault();
    toast.loading("Getting Student");
    const headers = {
      "Content-Type": "application/json",
    };
    dataService
      .getUsers(search)
      .then((response) => {
        toast.dismiss();
        if (response.success) {
          if (response.user.length === 0) {
            toast.dismiss();
            toast.error("No Student Found!");
          } else {
            toast.success(response.message);
            setData({
              enrollmentNo: response.user[0].enrollmentNo,
              firstName: response.user[0].firstName,
              middleName: response.user[0].middleName,
              lastName: response.user[0].lastName,
              email: response.user[0].email,
              phoneNumber: response.user[0].phoneNumber,
              semester: response.user[0].semester,
              branch: response.user[0].branch,
              gender: response.user[0].gender,
              profile: response.user[0].profile,
            });
            setId(response.user[0]._id);
          }
        } else {
          toast.dismiss();
          toast.error(response.message);
        }
      })
      .catch((error) => {
        toast.dismiss();
        toast.error(error.message);
        console.error(error);
      });
  };

  return (
    <div className="w-full mx-auto mt-10 flex justify-center items-start flex-col mb-10">
      <div className="flex justify-between items-center w-full">
        <Heading title="Student Details" />
      </div>
      <div className="my-6 mx-auto w-full">
        <form
          className="flex justify-center items-center border-2 border-blue-500 rounded w-[40%] mx-auto"
          onSubmit={searchStudentHandler}
        >
          <input
            type="text"
            className="px-6 py-3 w-full outline-none"
            placeholder="Enrollment No."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
          <button className="px-4 text-2xl hover:text-blue-500" type="submit">
            <FiSearch />
          </button>
        </form>
        {id && (
          <div className="mx-auto w-full bg-blue-50 mt-10 flex justify-between items-center p-10 rounded-md shadow-md">
            <div>
              <p className="text-2xl font-semibold">
                {data.firstName} {data.middleName} {data.lastName}
              </p>
              <div className="mt-3">
                <p className="text-lg font-normal mb-2">
                  Enrollment No: {data.enrollmentNo}
                </p>
                <p className="text-lg font-normal mb-2">
                  Phone Number: +91 {data.phoneNumber}
                </p>
                <p className="text-lg font-normal mb-2">
                  Email Address: {data.email}
                </p>
                <p className="text-lg font-normal mb-2">
                  Branch: {data.branch}
                </p>
                <p className="text-lg font-normal mb-2">
                  Semester: {data.semester}
                </p>
              </div>
            </div>
            <img
              src={process.env.REACT_APP_MEDIA_LINK + "/" + data.profile}
              alt="student profile"
              className="h-[200px] w-[200px] object-cover rounded-lg shadow-md"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default Student;
