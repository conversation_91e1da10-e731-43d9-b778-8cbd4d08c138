import React from 'react';
import { Routes, Route } from 'react-router-dom';
import AdminLayout from '../components/Layout/AdminLayout';
import CreateBatch from '../Screens/Admin/CreateBatch';
import Dashboard from '../Screens/Admin/Dashboard';
import Trainer from '../Screens/Admin/Trainer';
import College from '../Screens/Admin/College';
import AssignBatchToTrainers from '../Screens/Admin/AssignBatchToTrainers';
import UploadCourseWithTrainer from '../Screens/Admin/UploadCourseWithTrainer';
import ViewBatchWiseReports from '../Screens/Admin/ViewBatchWiseReports';
import ViewUserReports from '../Screens/Admin/ViewUserReports';
import UploadCertificate from '../Screens/Admin/UploadCertificate';
// Import other admin components as needed
const Students = () => <div>Students Management</div>;
const Faculty = () => <div>Faculty Management</div>;
const Courses = () => <div>Courses Management</div>;
const Schedule = () => <div>Schedule Management</div>;
const Attendance = () => <div>Attendance Management</div>;
const Settings = () => <div>Settings</div>;

const AdminRoutes = () => {
  return (
    <AdminLayout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/create-batch" element={<CreateBatch />} />
        <Route path="/trainer" element={<Trainer />} />
        <Route path="/college" element={<College />} />
        <Route path="/assign-batch" element={<AssignBatchToTrainers />} />
        <Route path="/upload-course" element={<UploadCourseWithTrainer />} />
        <Route path="/batch-reports" element={<ViewBatchWiseReports />} />
        <Route path="/user-reports" element={<ViewUserReports />} />
        <Route path="/upload-certificate" element={<UploadCertificate />} />
      </Routes>
    </AdminLayout>
  );
};

export default AdminRoutes;
