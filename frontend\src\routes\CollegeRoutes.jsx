import React from "react";
import { Routes, Route } from "react-router-dom";
import Home from "../Screens/College/Home";
import CollegeID from "../Screens/College/CollegeID";
import Department from "../Screens/College/Department";
import CSE from "../Screens/College/CSE";
import AIML from "../Screens/College/AIML";
import ISE from "../Screens/College/ISE";
import IOT from "../Screens/College/IOT";
import Civil from "../Screens/College/Civil";
import Mech from "../Screens/College/Mech";
import CSD from "../Screens/College/CSD";

const CollegeRoutes = () => (
  <Routes>
    <Route path="/" element={<Home />} />
    <Route path="/college-id" element={<CollegeID />} />
    <Route path="/department" element={<Department />} />
    <Route path="/cse" element={<CSE />} />
    <Route path="/aiml" element={<AIML />} />
    <Route path="/ise" element={<ISE />} />
    <Route path="/iot" element={<IOT />} />
    <Route path="/civil" element={<Civil />} />
    <Route path="/mech" element={<Mech />} />
    <Route path="/csd" element={<CSD />} />
    {/* Add more college routes as needed */}
  </Routes>
);

export default CollegeRoutes; 