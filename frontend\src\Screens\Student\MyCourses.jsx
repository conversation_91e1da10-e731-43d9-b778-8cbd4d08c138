import React, { useState } from "react";
import { FiBook, FiEye, FiPlus, FiX, FiCalendar, FiTarget, FiFileText, FiCheckCircle, FiDownload } from "react-icons/fi";

// Available courses for enrollment
const availableCourses = [
  { 
    id: 1, 
    courseTitle: "AutoCAD", 
    description: "Learn computer-aided design and drafting",
    instructor: "<PERSON><PERSON>",
    duration: "3 months",
    level: "Beginner",
    seats: 25,
    enrolled: 18,
    summary: "Master AutoCAD software for creating precise 2D and 3D designs. Learn essential tools, commands, and techniques used in engineering, architecture, and design industries.",
    outcomes: [
      "Create professional 2D drawings and 3D models",
      "Understand AutoCAD interface and tools",
      "Apply industry-standard design practices",
      "Generate technical documentation and blueprints"
    ],
    startDate: "2024-05-01",
    endDate: "2024-08-01",
    notes: "Course includes hands-on projects and real-world applications. Students will work on industry-standard projects to build their portfolio.",
    attendance: "85%",
    totalClasses: 20,
    attendedClasses: 17
  },
  { 
    id: 2, 
    courseTitle: "Full Stack Java", 
    description: "Complete web development with Java technologies",
    instructor: "<PERSON><PERSON> <PERSON>",
    duration: "6 months",
    level: "Intermediate",
    seats: 20,
    enrolled: 15,
    summary: "Comprehensive full-stack development course covering Java, Spring Boot, React, and database technologies. Build complete web applications from frontend to backend.",
    outcomes: [
      "Develop full-stack web applications",
      "Master Spring Boot framework",
      "Create responsive React frontends",
      "Implement RESTful APIs and database integration"
    ],
    startDate: "2024-06-15",
    endDate: "2024-12-15",
    notes: "Students will build a complete e-commerce application as their final project. Includes deployment and DevOps practices.",
    attendance: "92%",
    totalClasses: 25,
    attendedClasses: 23
  },
  { 
    id: 3, 
    courseTitle: "MERN Stack", 
    description: "Modern web development with MongoDB, Express, React, Node.js",
    instructor: "Prof. Williams",
    duration: "4 months",
    level: "Advanced",
    seats: 30,
    enrolled: 22,
    summary: "Learn modern web development using the MERN stack. Build scalable applications with MongoDB, Express.js, React, and Node.js.",
    outcomes: [
      "Build full-stack JavaScript applications",
      "Master MongoDB database design",
      "Create RESTful APIs with Express.js",
      "Develop dynamic React frontends"
    ],
    startDate: "2024-07-01",
    endDate: "2024-11-01",
    notes: "Focus on modern JavaScript practices and real-time applications. Students will create a social media platform.",
    attendance: "78%",
    totalClasses: 18,
    attendedClasses: 14
  },
  { 
    id: 4, 
    courseTitle: "Data Science", 
    description: "Master data analysis and machine learning",
    instructor: "Dr. Brown",
    duration: "5 months",
    level: "Intermediate",
    seats: 18,
    enrolled: 12,
    summary: "Comprehensive data science course covering statistics, machine learning, and data visualization. Learn to extract insights from complex datasets.",
    outcomes: [
      "Analyze and visualize complex datasets",
      "Implement machine learning algorithms",
      "Build predictive models",
      "Create data-driven business solutions"
    ],
    startDate: "2024-08-01",
    endDate: "2025-01-01",
    notes: "Students will work with real-world datasets and present their findings. Includes Python programming and statistical analysis.",
    attendance: "88%",
    totalClasses: 22,
    attendedClasses: 19
  },
  { 
    id: 5, 
    courseTitle: "Machine Learning", 
    description: "Advanced AI and machine learning algorithms",
    instructor: "Prof. Davis",
    duration: "6 months",
    level: "Advanced",
    seats: 15,
    enrolled: 10,
    summary: "Advanced machine learning course covering deep learning, neural networks, and AI applications. Learn to build intelligent systems.",
    outcomes: [
      "Implement deep learning algorithms",
      "Build neural network architectures",
      "Apply AI to real-world problems",
      "Deploy machine learning models"
    ],
    startDate: "2024-09-01",
    endDate: "2025-03-01",
    notes: "Advanced course requiring strong mathematical background. Students will develop AI applications for various domains.",
    attendance: "95%",
    totalClasses: 30,
    attendedClasses: 28
  },
  { 
    id: 6, 
    courseTitle: "Cloud Computing", 
    description: "AWS, Azure, and Google Cloud platforms",
    instructor: "Dr. Wilson",
    duration: "4 months",
    level: "Intermediate",
    seats: 22,
    enrolled: 16,
    summary: "Learn cloud computing fundamentals and deploy applications on major cloud platforms. Master AWS, Azure, and Google Cloud services.",
    outcomes: [
      "Deploy applications on cloud platforms",
      "Manage cloud infrastructure",
      "Implement DevOps practices",
      "Optimize cloud costs and performance"
    ],
    startDate: "2024-10-01",
    endDate: "2025-02-01",
    notes: "Hands-on labs with real cloud accounts. Students will deploy and manage applications across multiple cloud providers.",
    attendance: "82%",
    totalClasses: 24,
    attendedClasses: 20
  }
];

// Notes/Modules data - Actual PDF files uploaded by trainers
const notesModules = [
  { 
    id: 1, 
    title: "Module 1", 
    description: "Introduction to Course Fundamentals", 
    size: "2.5 MB", 
    date: "2024-01-15",
    pdfUrl: "/pdfs/module1.pdf",
    fileName: "module1.pdf",
    completed: false
  },
  { 
    id: 2, 
    title: "Module 2", 
    description: "Core Concepts and Basic Principles", 
    size: "3.1 MB", 
    date: "2024-01-22",
    pdfUrl: "/pdfs/module2.pdf",
    fileName: "module2.pdf",
    completed: false
  },
  { 
    id: 3, 
    title: "Module 3", 
    description: "Advanced Techniques and Applications", 
    size: "4.2 MB", 
    date: "2024-01-29",
    pdfUrl: "/pdfs/module3.pdf",
    fileName: "module3.pdf",
    completed: false
  },
  { 
    id: 4, 
    title: "Module 4", 
    description: "Practical Implementation and Projects", 
    size: "3.8 MB", 
    date: "2024-02-05",
    pdfUrl: "/pdfs/module4.pdf",
    fileName: "module4.pdf",
    completed: false
  },
  { 
    id: 5, 
    title: "Module 5", 
    description: "Final Assessment and Certification", 
    size: "2.9 MB", 
    date: "2024-02-12",
    pdfUrl: "/pdfs/module5.pdf",
    fileName: "module5.pdf",
    completed: false
  }
];

// Assessment Marks data
const assessmentMarks = [
  {
    id: 1,
    courseTitle: "AutoCAD",
    assessments: [
      { id: 1, title: "Mid-Term Assessment", date: "2024-06-15", marks: 85, totalMarks: 100, percentage: "85%" },
      { id: 2, title: "Final Project", date: "2024-07-20", marks: 92, totalMarks: 100, percentage: "92%" },
      { id: 3, title: "Practical Exam", date: "2024-08-01", marks: 78, totalMarks: 100, percentage: "78%" }
    ]
  },
  {
    id: 2,
    courseTitle: "Full Stack Java",
    assessments: [
      { id: 1, title: "Java Fundamentals", date: "2024-08-10", marks: 88, totalMarks: 100, percentage: "88%" },
      { id: 2, title: "Spring Boot Project", date: "2024-09-15", marks: 95, totalMarks: 100, percentage: "95%" },
      { id: 3, title: "Final Assessment", date: "2024-12-01", marks: 91, totalMarks: 100, percentage: "91%" }
    ]
  },
  {
    id: 3,
    courseTitle: "MERN Stack",
    assessments: [
      { id: 1, title: "JavaScript Basics", date: "2024-08-05", marks: 82, totalMarks: 100, percentage: "82%" },
      { id: 2, title: "React Development", date: "2024-09-10", marks: 89, totalMarks: 100, percentage: "89%" },
      { id: 3, title: "Full Stack Project", date: "2024-10-25", marks: 87, totalMarks: 100, percentage: "87%" }
    ]
  },
  {
    id: 4,
    courseTitle: "Data Science",
    assessments: [
      { id: 1, title: "Statistics Test", date: "2024-09-20", marks: 90, totalMarks: 100, percentage: "90%" },
      { id: 2, title: "Machine Learning", date: "2024-10-15", marks: 85, totalMarks: 100, percentage: "85%" },
      { id: 3, title: "Data Analysis Project", date: "2024-12-20", marks: 93, totalMarks: 100, percentage: "93%" }
    ]
  },
  {
    id: 5,
    courseTitle: "Machine Learning",
    assessments: [
      { id: 1, title: "Neural Networks", date: "2024-10-10", marks: 87, totalMarks: 100, percentage: "87%" },
      { id: 2, title: "Deep Learning", date: "2024-11-05", marks: 91, totalMarks: 100, percentage: "91%" },
      { id: 3, title: "AI Project", date: "2025-02-15", marks: 89, totalMarks: 100, percentage: "89%" }
    ]
  },
  {
    id: 6,
    courseTitle: "Cloud Computing",
    assessments: [
      { id: 1, title: "AWS Fundamentals", date: "2024-11-20", marks: 84, totalMarks: 100, percentage: "84%" },
      { id: 2, title: "Azure Services", date: "2024-12-10", marks: 88, totalMarks: 100, percentage: "88%" },
      { id: 3, title: "Cloud Deployment", date: "2025-01-25", marks: 86, totalMarks: 100, percentage: "86%" }
    ]
  }
];

// Assignment data
const assignments = [
  {
    id: 1,
    courseTitle: "AutoCAD",
    assignmentName: "2D Drawing Project",
    startDate: "2024-06-01",
    lastDate: "2024-06-15",
    description: "Create a detailed 2D drawing of a mechanical component using AutoCAD tools and commands.",
    status: "pending", // pending, submitted, graded
    uploadedFile: null,
    grade: null,
    feedback: null
  },
  {
    id: 2,
    courseTitle: "AutoCAD",
    assignmentName: "3D Modeling Assignment",
    startDate: "2024-06-20",
    lastDate: "2024-07-05",
    description: "Design a 3D model of a building structure with proper dimensions and annotations.",
    status: "submitted",
    uploadedFile: "assignment_3d_modeling.pdf",
    grade: 85,
    feedback: "Good work on the 3D modeling. Consider adding more detailed textures."
  },
  {
    id: 3,
    courseTitle: "Full Stack Java",
    assignmentName: "Spring Boot API",
    startDate: "2024-08-15",
    lastDate: "2024-08-30",
    description: "Create a RESTful API using Spring Boot with CRUD operations for a user management system.",
    status: "pending",
    uploadedFile: null,
    grade: null,
    feedback: null
  },
  {
    id: 4,
    courseTitle: "MERN Stack",
    assignmentName: "React Component Library",
    startDate: "2024-09-01",
    lastDate: "2024-09-15",
    description: "Build a reusable component library using React with TypeScript and Storybook.",
    status: "submitted",
    uploadedFile: "react_components.zip",
    grade: 92,
    feedback: "Excellent component design and documentation. Great use of TypeScript."
  },
  {
    id: 5,
    courseTitle: "Data Science",
    assignmentName: "Data Analysis Report",
    startDate: "2024-09-20",
    lastDate: "2024-10-05",
    description: "Analyze a dataset and create a comprehensive report with visualizations using Python.",
    status: "pending",
    uploadedFile: null,
    grade: null,
    feedback: null
  },
  {
    id: 6,
    courseTitle: "Machine Learning",
    assignmentName: "Neural Network Implementation",
    startDate: "2024-10-10",
    lastDate: "2024-10-25",
    description: "Implement a neural network from scratch using Python and NumPy for image classification.",
    status: "submitted",
    uploadedFile: "neural_network.py",
    grade: 88,
    feedback: "Good implementation. Consider optimizing the training process for better performance."
  }
];

const MyCourses = () => {
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedPdf, setSelectedPdf] = useState(null);
  const [showPdfModal, setShowPdfModal] = useState(false);
  const [selectedAssessment, setSelectedAssessment] = useState(null);
  const [showAssessmentModal, setShowAssessmentModal] = useState(false);
  const [modules, setModules] = useState(notesModules);
  const [assignmentList, setAssignmentList] = useState(assignments);
  const [selectedAssignment, setSelectedAssignment] = useState(null);
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);

  const getLevelColor = (level) => {
    switch (level) {
      case 'Beginner': return 'bg-green-100 text-green-700 border-green-200';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'Advanced': return 'bg-red-100 text-red-700 border-red-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getAvailabilityColor = (enrolled, seats) => {
    const percentage = (enrolled / seats) * 100;
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-green-600';
  };

  const handleViewCourse = (course) => {
    console.log('Selected course data:', course);
    setSelectedCourse(course);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedCourse(null);
  };

  const handleViewPdf = (module) => {
    setSelectedPdf(module);
    setShowPdfModal(true);
  };

  const closePdfModal = () => {
    setShowPdfModal(false);
    setSelectedPdf(null);
  };

  const handleDownloadModule = (module) => {
    // Create a link to download the actual PDF file
    const link = document.createElement('a');
    link.href = module.pdfUrl;
    link.download = module.fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Mark module as completed
    setModules(prevModules => 
      prevModules.map(m => 
        m.id === module.id ? { ...m, completed: true } : m
      )
    );
    
    // Show success message
    alert(`${module.title} downloaded successfully! Module marked as completed.`);
  };

  const handleViewAssessment = (assessment, courseTitle) => {
    setSelectedAssessment({ ...assessment, courseTitle });
    setShowAssessmentModal(true);
  };

  const closeAssessmentModal = () => {
    setShowAssessmentModal(false);
    setSelectedAssessment(null);
  };

  // Assignment handlers
  const handleViewAssignment = (assignment) => {
    setSelectedAssignment(assignment);
    setShowAssignmentModal(true);
  };

  const closeAssignmentModal = () => {
    setShowAssignmentModal(false);
    setSelectedAssignment(null);
    setUploadedFile(null);
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const handleSubmitAssignment = (assignmentId) => {
    if (!uploadedFile) {
      alert('Please select a file to upload');
      return;
    }

    // Simulate backend upload
    const formData = new FormData();
    formData.append('assignment', uploadedFile);
    formData.append('assignmentId', assignmentId);
    formData.append('studentId', '12345'); // Mock student ID

    // Here you would typically send to backend
    console.log('Uploading assignment:', {
      assignmentId,
      fileName: uploadedFile.name,
      fileSize: uploadedFile.size,
      fileType: uploadedFile.type
    });

    // Update assignment status
    setAssignmentList(prevAssignments =>
      prevAssignments.map(assignment =>
        assignment.id === assignmentId
          ? { ...assignment, status: 'submitted', uploadedFile: uploadedFile.name }
          : assignment
      )
    );

    alert('Assignment submitted successfully!');
    closeAssignmentModal();
  };

  return (
    <div className="w-full min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="bg-white p-3 rounded-full shadow-lg border-2 border-black">
                <FiBook className="text-3xl text-blue-600" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">My Courses</h1>
                <p className="text-gray-600 mt-2">Enroll in new courses and manage your learning journey.</p>
              </div>
            </div>
            <div className="hidden sm:flex items-center gap-2">
              <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors border-2 border-black">
                Get in Touch
              </button>
            </div>
          </div>
        </div>

        {/* Enroll New Courses Section */}
        <div className="bg-white rounded-lg shadow-sm border-2 border-black p-6 mb-6">
          <div className="mb-6">
            <h2 className="text-2xl font-semibold text-gray-900 mb-2 flex items-center gap-2">
              <FiPlus className="text-blue-500" />
              Enroll New Courses
            </h2>
            <p className="text-gray-600">Browse available courses and enroll in the ones that interest you.</p>
          </div>

          {/* Course Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {availableCourses.map((course) => (
              <div key={course.id} className="bg-gray-50 rounded-lg border-2 border-black shadow-lg hover:shadow-xl transition-all duration-200">
                <div className="p-6">
                  {/* Course Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="bg-blue-600 rounded-full p-3 border-2 border-black">
                      <FiBook className="text-white text-xl" />
                    </div>
                    <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium border-2 ${getLevelColor(course.level)}`}>
                      {course.level}
                    </span>
                  </div>

                  {/* Course Title */}
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{course.courseTitle}</h3>

                  {/* Course Description */}
                  <p className="text-gray-600 text-sm mb-4">{course.description}</p>

                  {/* Course Details */}
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Instructor:</span>
                      <span className="text-gray-700 font-medium">{course.instructor}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Duration:</span>
                      <span className="text-gray-700 font-medium">{course.duration}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Available Seats:</span>
                      <span className={`font-medium ${getAvailabilityColor(course.enrolled, course.seats)}`}>
                        {course.seats - course.enrolled} / {course.seats}
                      </span>
                    </div>
                  </div>

                  {/* View Button */}
                  <div className="flex justify-center">
                    <button 
                      onClick={() => handleViewCourse(course)}
                      className="text-blue-600 hover:text-blue-800 font-medium hover:underline flex items-center gap-2"
                    >
                      <FiEye className="w-4 h-4" />
                      View
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Notes Section */}
        <div id="trainer-notes-section" className="bg-white rounded-lg shadow-sm border-2 border-black p-6">
          <div className="mb-6">
            <h2 className="text-2xl font-semibold text-gray-900 mb-2 flex items-center gap-2">
              <FiFileText className="text-green-500" />
              Trainer Notes & Modules
            </h2>
            <p className="text-gray-600">View and download PDF notes uploaded by your trainers.</p>
          </div>

          {/* Notes Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {modules.map((module) => (
              <div key={module.id} className="bg-gray-50 rounded-lg border-2 border-black shadow-lg hover:shadow-xl transition-all duration-200">
                <div className="p-6">
                  {/* Module Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="bg-green-600 rounded-full p-3 border-2 border-black">
                      <FiFileText className="text-white text-xl" />
                    </div>
                    <span className="inline-block px-3 py-1 rounded-full text-xs font-medium border-2 bg-green-100 text-green-700 border-green-200">
                      PDF
                    </span>
                  </div>

                  {/* Module Title */}
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{module.title}</h3>

                  {/* Module Description */}
                  <p className="text-gray-600 text-sm mb-4">{module.description}</p>

                  {/* Module Details */}
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Size:</span>
                      <span className="text-gray-700 font-medium">{module.size}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Date:</span>
                      <span className="text-gray-700 font-medium">{module.date}</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-center">
                    <button 
                      onClick={() => handleDownloadModule(module)}
                      className="text-green-600 hover:text-green-800 font-medium hover:underline flex items-center gap-2"
                    >
                      <FiDownload className="w-4 h-4" />
                      Download
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Progress Timeline Section */}
        <div className="bg-white rounded-lg shadow-sm border-2 border-black p-6 mt-6">
          <div className="mb-6">
            <h2 className="text-2xl font-semibold text-gray-900 mb-2 flex items-center gap-2">
              <FiCheckCircle className="text-blue-500" />
              Module Progress
            </h2>
            <p className="text-gray-600">Track your progress through the course modules.</p>
          </div>

          {/* Timeline */}
          <div className="relative">
            {/* Progress Line */}
            <div 
              className="absolute top-8 left-0 h-2 bg-green-500 rounded-full transition-all duration-500"
              style={{ 
                width: `${(modules.filter(m => m.completed).length / modules.length) * 100}%`,
                zIndex: 5
              }}
            ></div>
            
            {/* Module Nodes */}
            <div className="relative flex justify-between items-center py-8">
              {modules.map((module, index) => (
                <div key={module.id} className="flex flex-col items-center">
                  {/* Module Node */}
                  <div 
                    className={`w-6 h-6 rounded-full border-4 border-white shadow-lg ${
                      module.completed ? 'bg-green-500' : 'bg-red-500'
                    }`}
                    style={{ zIndex: 10 }}
                  ></div>
                  
                  {/* Module Label */}
                  <div className="mt-3 text-center">
                    <span className="text-sm font-semibold text-gray-900">
                      {index < 2 ? `M${module.id}` : `m${module.id}`}
                    </span>
                    <div className="text-xs text-gray-500 mt-1">
                      {module.completed ? 'Completed' : 'Pending'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Assessment Marks Section */}
        <div className="bg-white rounded-lg shadow-sm border-2 border-black p-6 mt-6">
          <div className="mb-6">
            <h2 className="text-2xl font-semibold text-gray-900 mb-2 flex items-center gap-2">
              <FiTarget className="text-purple-500" />
              Assessment Marks
            </h2>
            <p className="text-gray-600">View your assessment marks and performance across all courses.</p>
          </div>

          {/* Assessment Marks Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-gray-300">
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">Course</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">Assessment</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">Date</th>
                  <th className="text-center py-3 px-4 font-semibold text-gray-900">Marks</th>
                  <th className="text-center py-3 px-4 font-semibold text-gray-900">Percentage</th>
                  <th className="text-center py-3 px-4 font-semibold text-gray-900">Status</th>
                  <th className="text-center py-3 px-4 font-semibold text-gray-900">Action</th>
                </tr>
              </thead>
              <tbody>
                {assessmentMarks.map((course) =>
                  course.assessments.map((assessment) => (
                    <tr key={`${course.id}-${assessment.id}`} className="border-b border-gray-200 hover:bg-gray-100 cursor-pointer">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{course.courseTitle}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{assessment.title}</div>
                      </td>
                      <td className="py-3 px-4 text-gray-700">{assessment.date}</td>
                      <td className="py-3 px-4 text-center">
                        <span className="font-semibold text-gray-900">
                          {assessment.marks}/{assessment.totalMarks}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                          assessment.marks >= 90 ? 'bg-green-100 text-green-700 border-green-200' :
                          assessment.marks >= 80 ? 'bg-blue-100 text-blue-700 border-blue-200' :
                          assessment.marks >= 70 ? 'bg-yellow-100 text-yellow-700 border-yellow-200' :
                          'bg-red-100 text-red-700 border-red-200'
                        } border-2`}>
                          {assessment.percentage}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                          assessment.marks >= 90 ? 'bg-green-100 text-green-700 border-green-200' :
                          assessment.marks >= 80 ? 'bg-blue-100 text-blue-700 border-blue-200' :
                          assessment.marks >= 70 ? 'bg-yellow-100 text-yellow-700 border-yellow-200' :
                          'bg-red-100 text-red-700 border-red-200'
                        } border-2`}>
                          {assessment.marks >= 90 ? 'Excellent' :
                           assessment.marks >= 80 ? 'Good' :
                           assessment.marks >= 70 ? 'Average' : 'Needs Improvement'}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <button 
                          onClick={() => handleViewAssessment(assessment, course.courseTitle)}
                          className="text-blue-600 hover:text-blue-800 font-medium hover:underline flex items-center gap-2 mx-auto"
                        >
                          <FiEye className="w-4 h-4" />
                          View Details
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Summary Statistics */}
          <div className="mt-6 pt-6 border-t-2 border-gray-300">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 rounded-lg p-4 border-2 border-blue-200">
                <div className="text-sm text-blue-600 font-medium">Total Assessments</div>
                <div className="text-2xl font-bold text-blue-900">
                  {assessmentMarks.reduce((sum, course) => sum + course.assessments.length, 0)}
                </div>
              </div>
              <div className="bg-green-50 rounded-lg p-4 border-2 border-green-200">
                <div className="text-sm text-green-600 font-medium">Average Score</div>
                <div className="text-2xl font-bold text-green-900">
                  {Math.round(assessmentMarks.reduce((sum, course) => 
                    sum + course.assessments.reduce((courseSum, assessment) => courseSum + assessment.marks, 0), 0
                  ) / assessmentMarks.reduce((sum, course) => sum + course.assessments.length, 0))}%
                </div>
              </div>
              <div className="bg-purple-50 rounded-lg p-4 border-2 border-purple-200">
                <div className="text-sm text-purple-600 font-medium">Courses Enrolled</div>
                <div className="text-2xl font-bold text-purple-900">{assessmentMarks.length}</div>
              </div>
            </div>
          </div>
                 </div>

         {/* Assignment Section */}
         <div className="bg-white rounded-lg shadow-sm border-2 border-black p-6 mt-6">
           <div className="mb-6">
             <h2 className="text-2xl font-semibold text-gray-900 mb-2 flex items-center gap-2">
               <FiFileText className="text-orange-500" />
               Assignments
             </h2>
             <p className="text-gray-600">View and submit your course assignments with deadlines and upload functionality.</p>
           </div>

           {/* Assignment Table */}
           <div className="overflow-x-auto">
             <table className="w-full">
               <thead>
                 <tr className="border-b-2 border-gray-300">
                   <th className="text-left py-3 px-4 font-semibold text-gray-900">Course</th>
                   <th className="text-left py-3 px-4 font-semibold text-gray-900">Assignment</th>
                   <th className="text-left py-3 px-4 font-semibold text-gray-900">Start Date</th>
                   <th className="text-left py-3 px-4 font-semibold text-gray-900">Last Date</th>
                   <th className="text-center py-3 px-4 font-semibold text-gray-900">Action</th>
                 </tr>
               </thead>
               <tbody>
                 {assignmentList.map((assignment) => (
                   <tr key={assignment.id} className="border-b border-gray-200 hover:bg-gray-100">
                     <td className="py-3 px-4">
                       <div className="font-medium text-gray-900">{assignment.courseTitle}</div>
                     </td>
                     <td className="py-3 px-4">
                       <div className="font-medium text-gray-900">{assignment.assignmentName}</div>
                     </td>
                     <td className="py-3 px-4 text-gray-700">{assignment.startDate}</td>
                     <td className="py-3 px-4 text-gray-700">{assignment.lastDate}</td>
                     <td className="py-3 px-4 text-center">
                       <button 
                         onClick={() => handleViewAssignment(assignment)}
                         className="text-blue-600 hover:text-blue-800 font-medium hover:underline flex items-center gap-2 mx-auto"
                       >
                         <FiEye className="w-4 h-4" />
                         View Details
                       </button>
                     </td>
                   </tr>
                 ))}
               </tbody>
             </table>
           </div>

           {/* Assignment Summary Statistics */}
           <div className="mt-6 pt-6 border-t-2 border-gray-300">
             <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
               <div className="bg-orange-50 rounded-lg p-4 border-2 border-orange-200">
                 <div className="text-sm text-orange-600 font-medium">Total Assignments</div>
                 <div className="text-2xl font-bold text-orange-900">{assignmentList.length}</div>
               </div>
               <div className="bg-green-50 rounded-lg p-4 border-2 border-green-200">
                 <div className="text-sm text-green-600 font-medium">Submitted</div>
                 <div className="text-2xl font-bold text-green-900">
                   {assignmentList.filter(a => a.status === 'submitted' || a.status === 'graded').length}
                 </div>
               </div>
               <div className="bg-yellow-50 rounded-lg p-4 border-2 border-yellow-200">
                 <div className="text-sm text-yellow-600 font-medium">Pending</div>
                 <div className="text-2xl font-bold text-yellow-900">
                   {assignmentList.filter(a => a.status === 'pending').length}
                 </div>
               </div>
               <div className="bg-blue-50 rounded-lg p-4 border-2 border-blue-200">
                 <div className="text-sm text-blue-600 font-medium">Average Grade</div>
                 <div className="text-2xl font-bold text-blue-900">
                   {assignmentList.filter(a => a.grade).length > 0 
                     ? Math.round(assignmentList.filter(a => a.grade).reduce((sum, a) => sum + a.grade, 0) / assignmentList.filter(a => a.grade).length)
                     : 0}%
                 </div>
               </div>
             </div>
           </div>
         </div>

         {/* Course Details Modal */}
        {showModal && selectedCourse && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded-lg shadow-xl border-2 border-black max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              {/* Modal Header */}
              <div className="flex items-center justify-between p-6 border-b-2 border-black">
                <h2 className="text-2xl font-bold text-gray-900">{selectedCourse.courseTitle}</h2>
                <button 
                  onClick={closeModal}
                  className="text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-gray-100"
                >
                  <FiX className="w-6 h-6" />
                </button>
              </div>

              {/* Modal Content */}
              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Left Column */}
                  <div className="space-y-6">
                    {/* Summary */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                        <FiFileText className="text-blue-500" />
                        Summary
                      </h3>
                      <p className="text-gray-700 leading-relaxed">{selectedCourse.summary}</p>
                    </div>

                    {/* Outcomes */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                        <FiTarget className="text-green-500" />
                        Learning Outcomes
                      </h3>
                      <ul className="space-y-2">
                        {selectedCourse.outcomes.map((outcome, index) => (
                          <li key={index} className="flex items-start gap-2 text-gray-700">
                            <FiCheckCircle className="text-green-500 mt-1 w-4 h-4 flex-shrink-0" />
                            <span>{outcome}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="space-y-6">
                    {/* Start Date */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                        <FiCalendar className="text-purple-500" />
                        Start Date
                      </h3>
                      <p className="text-gray-700 font-medium">{selectedCourse.startDate}</p>
                    </div>

                    {/* End Date */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                        <FiCalendar className="text-purple-500" />
                        End Date
                      </h3>
                      <p className="text-gray-700 font-medium">{selectedCourse.endDate}</p>
                    </div>

                    {/* Attendance */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                        <FiCheckCircle className="text-orange-500" />
                        Attendance
                      </h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Total Classes:</span>
                          <span className="text-gray-700 font-medium">{selectedCourse.totalClasses}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Attended Classes:</span>
                          <span className="text-gray-700 font-medium">{selectedCourse.attendedClasses}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Percentage:</span>
                          <span className="text-gray-700 font-medium">{selectedCourse.attendance}</span>
                        </div>
                      </div>
                    </div>

                    {/* Notes */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                        <FiFileText className="text-blue-500" />
                        Notes
                      </h3>
                      <p className="text-gray-700 mb-3">{selectedCourse.notes}</p>
                      <button 
                        onClick={() => {
                          closeModal();
                          // Scroll to the Trainer Notes & Modules section
                          const notesSection = document.getElementById('trainer-notes-section');
                          if (notesSection) {
                            notesSection.scrollIntoView({ behavior: 'smooth' });
                          }
                        }}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors border-2 border-black"
                      >
                        View Notes
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* PDF Viewer Modal */}
        {showPdfModal && selectedPdf && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded-lg shadow-xl border-2 border-black max-w-6xl w-full mx-4 h-[90vh] flex flex-col">
              {/* Modal Header */}
              <div className="flex items-center justify-between p-6 border-b-2 border-black">
                <h2 className="text-2xl font-bold text-gray-900">{selectedPdf.title}</h2>
                <div className="flex items-center gap-3">
                  <button 
                    onClick={() => handleDownloadModule(selectedPdf)}
                    className="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors border-2 border-black flex items-center gap-2"
                  >
                    <FiDownload className="w-4 h-4" />
                    Download
                  </button>
                  <button 
                    onClick={closePdfModal}
                    className="text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-gray-100"
                  >
                    <FiX className="w-6 h-6" />
                  </button>
                </div>
              </div>

              {/* PDF Viewer */}
              <div className="flex-1 p-6">
                <iframe
                  src={selectedPdf.pdfUrl}
                  className="w-full h-full border-2 border-gray-300 rounded-lg"
                  title={selectedPdf.title}
                />
              </div>
            </div>
          </div>
        )}

        {/* Assessment Details Modal */}
        {showAssessmentModal && selectedAssessment && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded-lg shadow-xl border-2 border-black max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              {/* Modal Header */}
              <div className="flex items-center justify-between p-6 border-b-2 border-black">
                <h2 className="text-2xl font-bold text-gray-900">Assessment Details</h2>
                <button 
                  onClick={closeAssessmentModal}
                  className="text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-gray-100"
                >
                  <FiX className="w-6 h-6" />
                </button>
              </div>

              {/* Modal Content */}
              <div className="p-6">
                <div className="space-y-6">
                  {/* Course Information */}
                  <div className="bg-blue-50 rounded-lg p-4 border-2 border-blue-200">
                    <h3 className="text-lg font-semibold text-blue-900 mb-2 flex items-center gap-2">
                      <FiBook className="text-blue-600" />
                      Course Information
                    </h3>
                    <p className="text-blue-800 font-medium">{selectedAssessment.courseTitle}</p>
                  </div>

                  {/* Assessment Details */}
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-gray-50 rounded-lg p-4 border-2 border-gray-200">
                        <h4 className="text-sm font-medium text-gray-600 mb-1">Assessment Title</h4>
                        <p className="text-lg font-semibold text-gray-900">{selectedAssessment.title}</p>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-4 border-2 border-gray-200">
                        <h4 className="text-sm font-medium text-gray-600 mb-1">Assessment Date</h4>
                        <p className="text-lg font-semibold text-gray-900">{selectedAssessment.date}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-green-50 rounded-lg p-4 border-2 border-green-200">
                        <h4 className="text-sm font-medium text-green-600 mb-1">Marks Obtained</h4>
                        <p className="text-2xl font-bold text-green-900">{selectedAssessment.marks}</p>
                      </div>
                      <div className="bg-blue-50 rounded-lg p-4 border-2 border-blue-200">
                        <h4 className="text-sm font-medium text-blue-600 mb-1">Total Marks</h4>
                        <p className="text-2xl font-bold text-blue-900">{selectedAssessment.totalMarks}</p>
                      </div>
                      <div className="bg-purple-50 rounded-lg p-4 border-2 border-purple-200">
                        <h4 className="text-sm font-medium text-purple-600 mb-1">Percentage</h4>
                        <p className="text-2xl font-bold text-purple-900">{selectedAssessment.percentage}</p>
                      </div>
                    </div>

                    {/* Performance Status */}
                    <div className="bg-gray-50 rounded-lg p-4 border-2 border-gray-200">
                      <h4 className="text-sm font-medium text-gray-600 mb-2">Performance Status</h4>
                      <div className="flex items-center gap-3">
                        <span className={`inline-block px-4 py-2 rounded-full text-sm font-medium ${
                          selectedAssessment.marks >= 90 ? 'bg-green-100 text-green-700 border-green-200' :
                          selectedAssessment.marks >= 80 ? 'bg-blue-100 text-blue-700 border-blue-200' :
                          selectedAssessment.marks >= 70 ? 'bg-yellow-100 text-yellow-700 border-yellow-200' :
                          'bg-red-100 text-red-700 border-red-200'
                        } border-2`}>
                          {selectedAssessment.marks >= 90 ? 'Excellent' :
                           selectedAssessment.marks >= 80 ? 'Good' :
                           selectedAssessment.marks >= 70 ? 'Average' : 'Needs Improvement'}
                        </span>
                        <span className="text-sm text-gray-600">
                          {selectedAssessment.marks >= 90 ? 'Outstanding performance! Keep up the excellent work.' :
                           selectedAssessment.marks >= 80 ? 'Good performance. You\'re on the right track.' :
                           selectedAssessment.marks >= 70 ? 'Average performance. Consider additional practice.' : 
                           'Performance needs improvement. Consider seeking additional help.'}
                        </span>
                      </div>
                    </div>

                    {/* Performance Analysis */}
                    <div className="bg-gray-50 rounded-lg p-4 border-2 border-gray-200">
                      <h4 className="text-sm font-medium text-gray-600 mb-2">Performance Analysis</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Score Ratio:</span>
                          <span className="font-semibold text-gray-900">{selectedAssessment.marks}/{selectedAssessment.totalMarks}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Percentage:</span>
                          <span className="font-semibold text-gray-900">{selectedAssessment.percentage}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Grade:</span>
                          <span className="font-semibold text-gray-900">
                            {selectedAssessment.marks >= 90 ? 'A+' :
                             selectedAssessment.marks >= 80 ? 'A' :
                             selectedAssessment.marks >= 70 ? 'B' :
                             selectedAssessment.marks >= 60 ? 'C' : 'D'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
                 )}

         {/* Assignment Details Modal */}
         {showAssignmentModal && selectedAssignment && (
           <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
             <div className="bg-white rounded-lg shadow-xl border-2 border-black max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
               {/* Modal Header */}
               <div className="flex items-center justify-between p-6 border-b-2 border-black">
                 <div>
                   <h2 className="text-2xl font-bold text-gray-900">{selectedAssignment.assignmentName}</h2>
                   <p className="text-gray-600 mt-1">{selectedAssignment.courseTitle}</p>
                 </div>
                 <button 
                   onClick={closeAssignmentModal}
                   className="text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-gray-100"
                 >
                   <FiX className="w-6 h-6" />
                 </button>
               </div>

               {/* Modal Content */}
               <div className="p-6">
                 <div className="space-y-6">
                   {/* Assignment Details */}
                   <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                     <div className="bg-blue-50 rounded-lg p-4 border-2 border-blue-200">
                       <h3 className="text-lg font-semibold text-blue-900 mb-3 flex items-center gap-2">
                         <FiCalendar className="text-blue-600" />
                         Assignment Details
                       </h3>
                       <div className="space-y-2">
                         <div className="flex justify-between">
                           <span className="text-blue-600 font-medium">Start Date:</span>
                           <span className="text-blue-900 font-semibold">{selectedAssignment.startDate}</span>
                         </div>
                         <div className="flex justify-between">
                           <span className="text-blue-600 font-medium">Last Date:</span>
                           <span className="text-blue-900 font-semibold">{selectedAssignment.lastDate}</span>
                         </div>
                         <div className="flex justify-between">
                           <span className="text-blue-600 font-medium">Status:</span>
                           <span className={`font-semibold ${
                             selectedAssignment.status === 'submitted' ? 'text-green-700' :
                             selectedAssignment.status === 'graded' ? 'text-blue-700' : 'text-yellow-700'
                           }`}>
                             {selectedAssignment.status === 'submitted' ? 'Submitted' :
                              selectedAssignment.status === 'graded' ? 'Graded' : 'Pending'}
                           </span>
                         </div>
                       </div>
                     </div>

                     <div className="bg-green-50 rounded-lg p-4 border-2 border-green-200">
                       <h3 className="text-lg font-semibold text-green-900 mb-3 flex items-center gap-2">
                         <FiFileText className="text-green-600" />
                         Submission Details
                       </h3>
                       <div className="space-y-2">
                         {selectedAssignment.uploadedFile ? (
                           <>
                             <div className="flex justify-between">
                               <span className="text-green-600 font-medium">Uploaded File:</span>
                               <span className="text-green-900 font-semibold">{selectedAssignment.uploadedFile}</span>
                             </div>
                             {selectedAssignment.grade && (
                               <div className="flex justify-between">
                                 <span className="text-green-600 font-medium">Grade:</span>
                                 <span className="text-green-900 font-semibold">{selectedAssignment.grade}%</span>
                               </div>
                             )}
                           </>
                         ) : (
                           <div className="text-green-600 font-medium">No file uploaded yet</div>
                         )}
                       </div>
                     </div>
                   </div>

                   {/* Assignment Description */}
                   <div className="bg-gray-50 rounded-lg p-4 border-2 border-gray-200">
                     <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                       <FiFileText className="text-gray-600" />
                       Assignment Description
                     </h3>
                     <p className="text-gray-700 leading-relaxed">{selectedAssignment.description}</p>
                   </div>

                   {/* Feedback Section */}
                   {selectedAssignment.feedback && (
                     <div className="bg-purple-50 rounded-lg p-4 border-2 border-purple-200">
                       <h3 className="text-lg font-semibold text-purple-900 mb-3 flex items-center gap-2">
                         <FiFileText className="text-purple-600" />
                         Instructor Feedback
                       </h3>
                       <p className="text-purple-700 leading-relaxed">{selectedAssignment.feedback}</p>
                     </div>
                   )}

                   {/* File Upload Section */}
                   {selectedAssignment.status === 'pending' && (
                     <div className="bg-orange-50 rounded-lg p-4 border-2 border-orange-200">
                       <h3 className="text-lg font-semibold text-orange-900 mb-3 flex items-center gap-2">
                         <FiDownload className="text-orange-600" />
                         Submit Assignment
                       </h3>
                       <div className="space-y-4">
                         <div>
                           <label className="block text-sm font-medium text-orange-700 mb-2">
                             Upload Assignment File (PDF, ZIP, DOC, etc.)
                           </label>
                           <input
                             type="file"
                             onChange={handleFileUpload}
                             accept=".pdf,.zip,.doc,.docx,.py,.js,.html,.css,.java,.cpp,.c"
                             className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-orange-50 file:text-orange-700 hover:file:bg-orange-100"
                           />
                         </div>
                         {uploadedFile && (
                           <div className="bg-white rounded-lg p-3 border-2 border-orange-200">
                             <p className="text-sm text-orange-700">
                               <span className="font-medium">Selected file:</span> {uploadedFile.name}
                             </p>
                             <p className="text-xs text-orange-600">
                               Size: {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                             </p>
                           </div>
                         )}
                         <button
                           onClick={() => handleSubmitAssignment(selectedAssignment.id)}
                           disabled={!uploadedFile}
                           className={`w-full py-3 px-4 rounded-lg font-semibold border-2 transition-colors ${
                             uploadedFile
                               ? 'bg-orange-600 text-white hover:bg-orange-700 border-orange-700'
                               : 'bg-gray-300 text-gray-500 border-gray-400 cursor-not-allowed'
                           }`}
                         >
                           Submit Assignment
                         </button>
                       </div>
                     </div>
                   )}
                 </div>
               </div>
             </div>
           </div>
         )}
       </div>
     </div>
   );
 };

export default MyCourses; 