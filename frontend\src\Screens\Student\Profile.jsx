import React, { useState, useRef } from "react";
import { 
  FiUser, 
  FiMail, 
  FiPhone, 
  FiMapPin, 
  FiMonitor, 
  FiCalendar, 
  FiHash, 
  FiImage, 
  FiX,
  FiDownload,
  FiShare2,
  FiAward,
  FiBookOpen,
  FiCode
} from "react-icons/fi";
import toast from "react-hot-toast";

const initialForm = {
  fullName: "Abhishe",
  email: "<EMAIL>",
  phone: "+91 98765 43210",
  location: "Bangalore, Karnataka",
  department: "Computer Science",
  year: "3rd Year",
  studentId: "CS2024001",
  cgpa: "8.5",
  credits: "120",
  skills: ["JavaScript", "React", "Node.js", "Python", "Java"],
  education: [
    {
      degree: "Bachelor of Engineering",
      institution: "Medini College of Engineering",
      year: "2021-2025",
      cgpa: "8.5"
    }
  ],
  achievements: [
    "First Prize in Hackathon 2024",
    "Best Project Award",
    "Dean's List 2023"
  ]
};

const Profile = () => {
  const [form, setForm] = useState(initialForm);
  const [activeTab, setActiveTab] = useState("personal");
  const [profilePic, setProfilePic] = useState(null);
  const [preview, setPreview] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState("");
  const [modalForm, setModalForm] = useState({});
  const fileInputRef = useRef();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleProfilePicChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setProfilePic(file);
      setPreview(URL.createObjectURL(file));
    }
  };

  const handleDownloadCV = () => {
    toast.success("CV download started!");
  };

  const handleShareProfile = () => {
    toast.success("Profile shared successfully!");
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
    if (isEditing) {
      toast.success("Edit mode disabled");
    } else {
      toast.success("Edit mode enabled");
    }
  };

  const validateEmail = (email) => /\S+@\S+\.\S+/.test(email);
  const validatePhone = (phone) => /^\+?\d{10,15}$/.test(phone);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validateEmail(form.email)) {
      toast.error("Please enter a valid email address.");
      return;
    }
    if (!validatePhone(form.phone)) {
      toast.error("Please enter a valid phone number.");
      return;
    }
    toast.success("Profile updated successfully!");
    setIsEditing(false);
  };

  const openModal = (type) => {
    setModalType(type);
    setModalForm({});
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setModalType("");
    setModalForm({});
  };

  const handleModalChange = (e) => {
    const { name, value } = e.target;
    setModalForm(prev => ({ ...prev, [name]: value }));
  };

  const handleModalSubmit = (e) => {
    e.preventDefault();
    if (modalType === "skill") {
      if (!modalForm.skill || !modalForm.skill.trim()) {
        toast.error("Please enter a skill");
        return;
      }
      setForm(prev => ({ 
        ...prev, 
        skills: [...prev.skills, modalForm.skill.trim()] 
      }));
      toast.success("Skill added successfully!");
    } else if (modalType === "achievement") {
      if (!modalForm.achievement || !modalForm.achievement.trim()) {
        toast.error("Please enter an achievement");
        return;
      }
      setForm(prev => ({ 
        ...prev, 
        achievements: [...prev.achievements, modalForm.achievement.trim()] 
      }));
      toast.success("Achievement added successfully!");
    } else if (modalType === "education") {
      if (!modalForm.degree || !modalForm.institution || !modalForm.year || !modalForm.cgpa) {
        toast.error("Please fill all fields");
        return;
      }
      setForm(prev => ({ 
        ...prev, 
        education: [...prev.education, {
          degree: modalForm.degree.trim(),
          institution: modalForm.institution.trim(),
          year: modalForm.year.trim(),
          cgpa: modalForm.cgpa.trim()
        }]
      }));
      toast.success("Education entry added successfully!");
    }
    
    closeModal();
  };

  const tabs = [
    { id: "personal", label: "Personal", icon: FiUser },
    { id: "skills", label: "Skills", icon: FiCode },
    { id: "education", label: "Education", icon: FiBookOpen },
    { id: "achievements", label: "Achievements", icon: FiAward }
  ];

  return (
    <div className="w-full min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
            <p className="text-gray-700 mt-1">Manage your personal information and academic details</p>
          </div>
          <button 
            onClick={handleEditToggle}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
              isEditing 
                ? "bg-red-600 text-white hover:bg-red-700" 
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
          >
            {isEditing ? <FiX size={16} /> : <FiUser size={16} />}
            {isEditing ? "Cancel" : "Edit"}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Profile Summary */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
              {/* Profile Avatar */}
              <div className="flex flex-col items-center mb-6">
                <div className="relative w-32 h-32 mb-4">
                  <img
                    src={preview || "https://ui-avatars.com/api/?name=AB&background=3B82F6&color=ffffff&size=128"}
                    alt="Profile"
                    className="w-32 h-32 rounded-full object-cover border-4 border-blue-100"
              />
              <button
                type="button"
                onClick={() => fileInputRef.current.click()}
                    className="absolute bottom-2 right-2 bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
                title="Change profile picture"
              >
                    <FiImage size={16} />
              </button>
              <input
                type="file"
                accept="image/*"
                ref={fileInputRef}
                onChange={handleProfilePicChange}
                className="hidden"
              />
            </div>
                
                {/* Student Info */}
                <h2 className="text-2xl font-bold text-gray-900 mb-1">{form.fullName}</h2>
                <p className="text-gray-800 mb-1">{form.department}</p>
                <p className="text-sm text-gray-700">Student ID: {form.studentId}</p>
          </div>

              {/* Academic Metrics */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-gray-50 rounded-lg p-4 text-center shadow-lg border-2 border-black">
                  <div className="text-2xl font-bold text-blue-600">{form.cgpa}</div>
                  <div className="text-sm text-gray-800">CGPA</div>
            </div>
                <div className="bg-gray-50 rounded-lg p-4 text-center shadow-lg border-2 border-black">
                  <div className="text-2xl font-bold text-green-600">{form.credits}</div>
                  <div className="text-sm text-gray-800">Credits</div>
            </div>
          </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <button
                  onClick={handleDownloadCV}
                  className="w-full bg-gray-100 border-2 border-black text-black px-4 py-3 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-200 transition-colors"
                >
                  <FiDownload size={16} />
                  Download CV
                </button>
                <button
                  onClick={handleShareProfile}
                  className="w-full bg-gray-100 border-2 border-black text-black px-4 py-3 rounded-lg flex items-center justify-center gap-2 hover:bg-gray-200 transition-colors"
                >
                  <FiShare2 size={16} />
                  Share Profile
                </button>
              </div>
            </div>
          </div>

          {/* Right Panel - Detailed Information */}
          <div className="lg:col-span-2">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
              {/* Tabs */}
              <div className="border-b border-gray-200">
                <div className="flex space-x-8 px-6">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? "border-blue-600 text-blue-600"
                          : "border-transparent text-gray-600 hover:text-gray-800"
                      }`}
                    >
                      <tab.icon size={16} />
                      {tab.label}
                    </button>
                  ))}
            </div>
          </div>

              {/* Tab Content */}
              <div className="p-6">
                {activeTab === "personal" && (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <FiMail className="text-blue-500" size={20} />
              <input
                name="email"
                type="email"
                value={form.email}
                onChange={handleChange}
                placeholder="Email"
                          disabled={!isEditing}
                          className={`flex-1 border border-gray-200 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800 ${
                            isEditing ? "bg-gray-50" : "bg-gray-100 cursor-not-allowed"
                          }`}
              />
            </div>
                      <div className="flex items-center gap-3">
                        <FiPhone className="text-blue-500" size={20} />
                        <input
                          name="phone"
                          type="tel"
                          value={form.phone}
                          onChange={handleChange}
                          placeholder="Phone"
                          disabled={!isEditing}
                          className={`flex-1 border border-gray-200 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800 ${
                            isEditing ? "bg-gray-50" : "bg-gray-100 cursor-not-allowed"
                          }`}
                        />
          </div>
                      <div className="flex items-center gap-3">
                        <FiMapPin className="text-blue-500" size={20} />
              <input
                          name="location"
                          value={form.location}
                onChange={handleChange}
                          placeholder="Location"
                          disabled={!isEditing}
                          className={`flex-1 border border-gray-200 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800 ${
                            isEditing ? "bg-gray-50" : "bg-gray-100 cursor-not-allowed"
                          }`}
              />
            </div>
                      <div className="flex items-center gap-3">
                        <FiMonitor className="text-blue-500" size={20} />
              <input
                name="department"
                value={form.department}
                onChange={handleChange}
                placeholder="Department"
                          disabled={!isEditing}
                          className={`flex-1 border border-gray-200 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800 ${
                            isEditing ? "bg-gray-50" : "bg-gray-100 cursor-not-allowed"
                          }`}
                        />
                      </div>
                      <div className="flex items-center gap-3">
                        <FiCalendar className="text-blue-500" size={20} />
                        <input
                          name="year"
                          value={form.year}
                          onChange={handleChange}
                          placeholder="Year"
                          disabled={!isEditing}
                          className={`flex-1 border border-gray-200 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800 ${
                            isEditing ? "bg-gray-50" : "bg-gray-100 cursor-not-allowed"
                          }`}
                        />
                      </div>
                      <div className="flex items-center gap-3">
                        <FiHash className="text-blue-500" size={20} />
                        <input
                          name="studentId"
                          value={form.studentId}
                          onChange={handleChange}
                          placeholder="Student ID"
                          disabled={!isEditing}
                          className={`flex-1 border border-gray-200 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800 ${
                            isEditing ? "bg-gray-50" : "bg-gray-100 cursor-not-allowed"
                          }`}
              />
            </div>
                      <div className="flex items-center gap-3">
                        <FiHash className="text-blue-500" size={20} />
                        <input
                          name="cgpa"
                          value={form.cgpa}
                onChange={handleChange}
                          placeholder="CGPA"
                          disabled={!isEditing}
                          className={`flex-1 border border-gray-200 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800 ${
                            isEditing ? "bg-gray-50" : "bg-gray-100 cursor-not-allowed"
                          }`}
                        />
                      </div>
                    </div>
                    {isEditing && (
                      <button
                        type="submit"
                        className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors"
                      >
                        Save Changes
                      </button>
                    )}
                  </form>
                )}

                {activeTab === "skills" && (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-semibold text-gray-900">Skills</h3>
                      <button
                        onClick={() => openModal("skill")}
                        className="bg-gray-100 border-2 border-black text-black px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                      >
                        Add Skill
                      </button>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {form.skills.map((skill, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-3 border-2 border-black">
                          <span className="text-gray-900 font-medium">{skill}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {activeTab === "education" && (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-semibold text-gray-900">Education</h3>
                      <button
                        onClick={() => openModal("education")}
                        className="bg-gray-100 border-2 border-black text-black px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                      >
                        Add Education
                      </button>
                    </div>
                    <div className="space-y-4">
                      {form.education.map((edu, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-4 border-2 border-black">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-semibold text-gray-900">{edu.degree}</h4>
                              <p className="text-gray-800">{edu.institution}</p>
                              <p className="text-sm text-gray-700">{edu.year}</p>
                            </div>
                            <span className="text-blue-600 font-bold">CGPA: {edu.cgpa}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {activeTab === "achievements" && (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-semibold text-gray-900">Achievements</h3>
                      <button
                        onClick={() => openModal("achievement")}
                        className="bg-gray-100 border-2 border-black text-black px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                      >
                        Add Achievement
                      </button>
                    </div>
                    <div className="space-y-3">
                      {form.achievements.map((achievement, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-4 border-2 border-black">
                          <div className="flex items-center gap-3">
                            <FiAward className="text-yellow-500" size={20} />
                            <span className="text-gray-900">{achievement}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              {modalType === "skill" && "Add New Skill"}
              {modalType === "achievement" && "Add New Achievement"}
              {modalType === "education" && "Add New Education"}
            </h3>
            <form onSubmit={handleModalSubmit} className="space-y-4">
              {modalType === "skill" && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Skill Name
                  </label>
                  <input
                    type="text"
                    name="skill"
                    value={modalForm.skill || ""}
                    onChange={handleModalChange}
                    placeholder="Enter skill name"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800"
                    required
                  />
                </div>
              )}
              
              {modalType === "achievement" && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Achievement
                  </label>
                  <textarea
                    name="achievement"
                    value={modalForm.achievement || ""}
                    onChange={handleModalChange}
                    placeholder="Enter your achievement"
                    rows="3"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800"
                    required
                  />
                </div>
              )}
              
              {modalType === "education" && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Degree
                    </label>
                    <input
                      type="text"
                      name="degree"
                      value={modalForm.degree || ""}
                      onChange={handleModalChange}
                      placeholder="e.g., Bachelor of Engineering"
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Institution
                    </label>
                    <input
                      type="text"
                      name="institution"
                      value={modalForm.institution || ""}
                      onChange={handleModalChange}
                      placeholder="e.g., Medini College of Engineering"
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Year
                      </label>
                      <input
                        type="text"
                        name="year"
                        value={modalForm.year || ""}
                        onChange={handleModalChange}
                        placeholder="e.g., 2021-2025"
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        CGPA
                      </label>
              <input
                        type="text"
                        name="cgpa"
                        value={modalForm.cgpa || ""}
                        onChange={handleModalChange}
                        placeholder="e.g., 8.5"
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-800"
                required
              />
            </div>
          </div>
                </div>
              )}
              
              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={closeModal}
                  className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  Cancel
                </button>
          <button
            type="submit"
                  className="flex-1 bg-gray-100 border-2 border-black text-black px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
          >
                  Add
          </button>
              </div>
        </form>
      </div>
        </div>
      )}
    </div>
  );
};

export default Profile;
