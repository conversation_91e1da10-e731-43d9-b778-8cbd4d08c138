import React from "react";
import { FiArrowLeft } from "react-icons/fi";
import { useNavigate } from "react-router-dom";

const CollegeID = () => {
  const navigate = useNavigate();
  const collegeId = "CLG-2024-001";
  return (
    <>
      <nav className="w-full shadow flex items-center justify-between px-6 py-4 mb-4 bg-gradient-to-r" style={{ background: 'linear-gradient(90deg,rgb(35, 65, 75) 0%,rgb(35, 65, 75) 100%)' }}>
        <div className="flex items-center gap-3">
          <button onClick={() => navigate(-1)} className="text-white hover:bg-white/10 rounded-full p-2 mr-2" title="Back">
            <FiArrowLeft size={22} />
          </button>
        </div>
        <div>
          <img src="/images/Medini logo White.png" alt="Logo" className="h-12 rounded" />
        </div>
      </nav>
      <div className="max-w-xl mx-auto mt-10 bg-white rounded-xl shadow p-8 text-center">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">College ID</h1>
        <div className="text-2xl font-mono font-bold text-blue-700 bg-blue-50 rounded p-4 inline-block">{collegeId}</div>
      </div>
    </>
  );
};

export default CollegeID; 