import React, { useState, useEffect } from 'react';
import { 
  FiUsers, 
  FiBook, 
  FiHome, 
  FiUpload, 
  FiBarChart2, 
  FiAward, 
  FiPlus, 
  FiEdit2, 
  FiTrash2, 
  FiEye, 
  FiX, 
  FiSave, 
  FiRefreshCw,
  FiUser,
  FiMail,
  FiPhone,
  FiBookOpen,
  FiCalendar,
  FiMapPin,
  FiCheckCircle,
  FiFile,
  FiLayers,
  FiTrendingUp,
  FiUsers as FiUsersIcon,
  FiAward as FiAwardIcon
} from 'react-icons/fi';


const Dashboard = () => {
  const [isEditing, setIsEditing] = useState(false);
  
  // Mock data for all sections
  const batches = [
    { id: 1, name: 'Batch <PERSON>', department: 'CS', trainer: '<PERSON>', students: 25, status: 'Active' },
    { id: 2, name: '<PERSON>ch <PERSON>', department: 'ECE', trainer: '<PERSON>', students: 30, status: 'Active' },
    { id: 3, name: '<PERSON><PERSON> <PERSON>', department: '<PERSON><PERSON>', trainer: '<PERSON>', students: 20, status: 'Active' }
  ];

  const trainers = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', specialization: 'CS', batches: 2, status: 'Active' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', specialization: 'ECE', batches: 1, status: 'Active' },
    { id: 3, name: 'Mike Wilson', email: '<EMAIL>', specialization: 'MECH', batches: 1, status: 'Active' }
  ];

  const colleges = [
    { id: 1, name: 'Medini College', location: 'Bangalore', contact: '<EMAIL>', departments: 5, status: 'Active' },
    { id: 2, name: 'Tech University', location: 'Mumbai', contact: '<EMAIL>', departments: 3, status: 'Active' },
    { id: 3, name: 'Engineering Institute', location: 'Delhi', contact: '<EMAIL>', departments: 4, status: 'Active' }
  ];

  const courses = [
    { id: 1, title: 'Web Development', trainer: 'John Smith', department: 'CS', college: 'Medini College', file: 'web_dev.pdf', status: 'Active' },
    { id: 2, title: 'Data Science', trainer: 'Sarah Johnson', department: 'ECE', college: 'Tech University', file: 'data_science.pdf', status: 'Active' },
    { id: 3, title: 'Machine Learning', trainer: 'Mike Wilson', department: 'MECH', college: 'Engineering Institute', file: 'ml.pdf', status: 'Active' }
  ];

  // Mock data for certificates (keeping this as mock for now)
  const certificates = [
    { id: 1, name: 'Java Certification', student: 'Alice Brown', file: 'java_cert.pdf', issueDate: '2024-07-01', status: 'Issued' },
    { id: 2, name: 'Python Certification', student: 'Bob Johnson', file: 'python_cert.pdf', issueDate: '2024-08-15', status: 'Issued' },
    { id: 3, name: 'Web Dev Certification', student: 'Charlie Davis', file: 'web_cert.pdf', issueDate: '2024-09-20', status: 'Issued' }
  ];

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const handleDeleteBatch = (id) => {
    // Mock delete - in real app this would call API
    console.log('Delete batch:', id);
  };

  const handleDeleteTrainer = (id) => {
    // Mock delete - in real app this would call API
    console.log('Delete trainer:', id);
  };

  const handleDeleteCollege = (id) => {
    // Mock delete - in real app this would call API
    console.log('Delete college:', id);
  };

  const handleDeleteCourse = (id) => {
    // Mock delete - in real app this would call API
    console.log('Delete course:', id);
  };

  const handleRefreshBatches = () => {
    // Mock refresh - in real app this would reload data
    console.log('Refresh batches');
  };

  const handleRefreshTrainers = () => {
    // Mock refresh - in real app this would reload data
    console.log('Refresh trainers');
  };

  const handleRefreshColleges = () => {
    // Mock refresh - in real app this would reload data
    console.log('Refresh colleges');
  };

  const handleRefreshCourses = () => {
    // Mock refresh - in real app this would reload data
    console.log('Refresh courses');
  };

  return (
    <div className="w-full min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="text-gray-700 mt-1">Overview of all batches, trainers, colleges, courses, and certificates</p>
          </div>
          <button 
            onClick={handleEditToggle}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
              isEditing 
                ? "bg-red-600 text-white hover:bg-red-700" 
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
          >
            {isEditing ? <FiX size={16} /> : <FiPlus size={16} />}
            {isEditing ? "Cancel" : "Edit Mode"}
          </button>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6 text-center">
            <FiLayers className="text-4xl text-blue-500 mx-auto mb-3" />
            <h3 className="text-2xl font-bold text-gray-900">{batches.length}</h3>
            <p className="text-gray-600">Total Batches</p>
          </div>
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6 text-center">
            <FiUser className="text-4xl text-green-500 mx-auto mb-3" />
            <h3 className="text-2xl font-bold text-gray-900">{trainers.length}</h3>
            <p className="text-gray-600">Total Trainers</p>
          </div>
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6 text-center">
            <FiHome className="text-4xl text-purple-500 mx-auto mb-3" />
            <h3 className="text-2xl font-bold text-gray-900">{colleges.length}</h3>
            <p className="text-gray-600">Total Colleges</p>
          </div>
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6 text-center">
            <FiBookOpen className="text-4xl text-orange-500 mx-auto mb-3" />
            <h3 className="text-2xl font-bold text-gray-900">{courses.length}</h3>
            <p className="text-gray-600">Total Courses</p>
          </div>
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6 text-center">
            <FiAwardIcon className="text-4xl text-yellow-500 mx-auto mb-3" />
            <h3 className="text-2xl font-bold text-gray-900">{certificates.length}</h3>
            <p className="text-gray-600">Total Certificates</p>
          </div>
        </div>

        {/* Main Content - Horizontal Sections */}
        <div className="space-y-8">
          {/* Batches Section */}
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
            <div className="border-b border-gray-200">
              <div className="flex justify-between items-center px-6">
                <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-blue-600 font-medium text-sm text-blue-600">
                  <FiLayers size={16} />
                  All Batches
                </button>
                <button 
                  onClick={handleRefreshBatches}
                  className="bg-blue-600 text-white p-2 rounded hover:bg-blue-700 transition-colors border border-blue-600"
                >
                  <FiRefreshCw className="text-sm" />
                </button>
              </div>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {batches.length === 0 ? (
                  <div className="col-span-full text-center py-8">
                    <FiLayers className="text-4xl text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500 text-base">No batches created yet</p>
                    <p className="text-gray-400 text-sm mt-1">Create your first batch to get started</p>
                  </div>
                ) : (
                  batches.map((batch) => (
                    <div key={batch.id} className="bg-gray-50 rounded-lg p-3 border-2 border-black hover:shadow-lg transition-all duration-200">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-bold border border-white shadow-lg">
                              {batch.department}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded border border-black ${
                              batch.status === 'Active' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                            }`}>
                              {batch.status}
                            </span>
                          </div>
                          <h4 className="text-base font-bold text-gray-900 mb-2">{batch.name}</h4>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="flex items-center text-gray-700 bg-gray-100 p-1.5 rounded border border-black">
                              <FiUser className="mr-1 text-blue-500 text-xs" />
                              <span className="font-medium text-xs">{batch.trainer}</span>
                            </div>
                            <div className="flex items-center text-gray-700 bg-gray-100 p-1.5 rounded border border-black">
                              <FiUsersIcon className="mr-1 text-green-500 text-xs" />
                              <span className="font-medium text-xs">{batch.students} Students</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-1 ml-2">
                          <button className="bg-blue-600 text-white p-1 rounded hover:bg-blue-700 transition-colors border border-blue-600">
                            <FiEye className="text-xs" />
                          </button>
                          <button className="bg-green-600 text-white p-1 rounded hover:bg-green-700 transition-colors border border-green-600">
                            <FiEdit2 className="text-xs" />
                          </button>
                          <button 
                            onClick={() => handleDeleteBatch(batch.id)}
                            className="bg-red-600 text-white p-1 rounded hover:bg-red-700 transition-colors border border-red-600"
                          >
                            <FiTrash2 className="text-xs" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Trainers Section */}
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
            <div className="border-b border-gray-200">
              <div className="flex justify-between items-center px-6">
                <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-green-600 font-medium text-sm text-green-600">
                  <FiUser size={16} />
                  All Trainers
                </button>
                <button 
                  onClick={handleRefreshTrainers}
                  className="bg-green-600 text-white p-2 rounded hover:bg-green-700 transition-colors border border-green-600"
                >
                  <FiRefreshCw className="text-sm" />
                </button>
              </div>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {trainers.length === 0 ? (
                  <div className="col-span-full text-center py-8">
                    <FiUser className="text-4xl text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500 text-base">No trainers added yet</p>
                    <p className="text-gray-400 text-sm mt-1">Add your first trainer to get started</p>
                  </div>
                ) : (
                  trainers.map((trainer) => (
                    <div key={trainer.id} className="bg-gray-50 rounded-lg p-3 border-2 border-black hover:shadow-lg transition-all duration-200">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold border border-white shadow-lg">
                              {trainer.specialization}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded border border-black ${
                              trainer.status === 'Active' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                            }`}>
                              {trainer.status}
                            </span>
                          </div>
                          <h4 className="text-base font-bold text-gray-900 mb-2">{trainer.name}</h4>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="flex items-center text-gray-700 bg-gray-100 p-1.5 rounded border border-black">
                              <FiMail className="mr-1 text-blue-500 text-xs" />
                              <span className="font-medium text-xs">{trainer.email}</span>
                            </div>
                            <div className="flex items-center text-gray-700 bg-gray-100 p-1.5 rounded border border-black">
                              <FiLayers className="mr-1 text-purple-500 text-xs" />
                              <span className="font-medium text-xs">{trainer.batches} Batches</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-1 ml-2">
                          <button className="bg-blue-600 text-white p-1 rounded hover:bg-blue-700 transition-colors border border-blue-600">
                            <FiEye className="text-xs" />
                          </button>
                          <button className="bg-green-600 text-white p-1 rounded hover:bg-green-700 transition-colors border border-green-600">
                            <FiEdit2 className="text-xs" />
                          </button>
                          <button 
                            onClick={() => handleDeleteTrainer(trainer.id)}
                            className="bg-red-600 text-white p-1 rounded hover:bg-red-700 transition-colors border border-red-600"
                          >
                            <FiTrash2 className="text-xs" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Colleges Section */}
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
            <div className="border-b border-gray-200">
              <div className="flex justify-between items-center px-6">
                <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-purple-600 font-medium text-sm text-purple-600">
                  <FiHome size={16} />
                  All Colleges
                </button>
                <button 
                  onClick={handleRefreshColleges}
                  className="bg-purple-600 text-white p-2 rounded hover:bg-purple-700 transition-colors border border-purple-600"
                >
                  <FiRefreshCw className="text-sm" />
                </button>
              </div>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {colleges.length === 0 ? (
                  <div className="col-span-full text-center py-8">
                    <FiHome className="text-4xl text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500 text-base">No colleges added yet</p>
                    <p className="text-gray-400 text-sm mt-1">Add your first college to get started</p>
                  </div>
                ) : (
                  colleges.map((college) => (
                    <div key={college.id} className="bg-gray-50 rounded-lg p-3 border-2 border-black hover:shadow-lg transition-all duration-200">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-purple-500 text-white px-2 py-1 rounded-full text-xs font-bold border border-white shadow-lg">
                              {college.departments} Depts
                            </span>
                            <span className={`text-xs px-2 py-1 rounded border border-black ${
                              college.status === 'Active' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                            }`}>
                              {college.status}
                            </span>
                          </div>
                          <h4 className="text-base font-bold text-gray-900 mb-2">{college.name}</h4>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="flex items-center text-gray-700 bg-gray-100 p-1.5 rounded border border-black">
                              <FiMapPin className="mr-1 text-blue-500 text-xs" />
                              <span className="font-medium text-xs">{college.location}</span>
                            </div>
                            <div className="flex items-center text-gray-700 bg-gray-100 p-1.5 rounded border border-black">
                              <FiPhone className="mr-1 text-green-500 text-xs" />
                              <span className="font-medium text-xs">{college.contact}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-1 ml-2">
                          <button className="bg-blue-600 text-white p-1 rounded hover:bg-blue-700 transition-colors border border-blue-600">
                            <FiEye className="text-xs" />
                          </button>
                          <button className="bg-green-600 text-white p-1 rounded hover:bg-green-700 transition-colors border border-green-600">
                            <FiEdit2 className="text-xs" />
                          </button>
                          <button 
                            onClick={() => handleDeleteCollege(college.id)}
                            className="bg-red-600 text-white p-1 rounded hover:bg-red-700 transition-colors border border-red-600"
                          >
                            <FiTrash2 className="text-xs" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Courses Section */}
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
            <div className="border-b border-gray-200">
              <div className="flex justify-between items-center px-6">
                <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-orange-600 font-medium text-sm text-orange-600">
                  <FiBookOpen size={16} />
                  All Courses
                </button>
                <button 
                  onClick={handleRefreshCourses}
                  className="bg-orange-600 text-white p-2 rounded hover:bg-orange-700 transition-colors border border-orange-600"
                >
                  <FiRefreshCw className="text-sm" />
                </button>
              </div>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {courses.length === 0 ? (
                  <div className="col-span-full text-center py-8">
                    <FiBookOpen className="text-4xl text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500 text-base">No courses uploaded yet</p>
                    <p className="text-gray-400 text-sm mt-1">Upload your first course to get started</p>
                  </div>
                ) : (
                  courses.map((course) => (
                    <div key={course.id} className="bg-gray-50 rounded-lg p-3 border-2 border-black hover:shadow-lg transition-all duration-200">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold border border-white shadow-lg">
                              {course.students} Students
                            </span>
                            <span className={`text-xs px-2 py-1 rounded border border-black ${
                              course.status === 'Active' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                            }`}>
                              {course.status}
                            </span>
                          </div>
                          <h4 className="text-base font-bold text-gray-900 mb-2">{course.name}</h4>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="flex items-center text-gray-700 bg-gray-100 p-1.5 rounded border border-black">
                              <FiUser className="mr-1 text-blue-500 text-xs" />
                              <span className="font-medium text-xs">{course.trainer}</span>
                            </div>
                            <div className="flex items-center text-gray-700 bg-gray-100 p-1.5 rounded border border-black">
                              <FiFile className="mr-1 text-green-500 text-xs" />
                              <span className="font-medium text-xs">{course.file}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-1 ml-2">
                          <button className="bg-blue-600 text-white p-1 rounded hover:bg-blue-700 transition-colors border border-blue-600">
                            <FiEye className="text-xs" />
                          </button>
                          <button className="bg-green-600 text-white p-1 rounded hover:bg-green-700 transition-colors border border-green-600">
                            <FiEdit2 className="text-xs" />
                          </button>
                          <button 
                            onClick={() => handleDeleteCourse(course.id)}
                            className="bg-red-600 text-white p-1 rounded hover:bg-red-700 transition-colors border border-red-600"
                          >
                            <FiTrash2 className="text-xs" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Certificates Section */}
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
            <div className="border-b border-gray-200">
              <div className="flex space-x-8 px-6">
                <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-yellow-600 font-medium text-sm text-yellow-600">
                  <FiAwardIcon size={16} />
                  All Certificates
                </button>
              </div>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {certificates.length === 0 ? (
                  <div className="col-span-full text-center py-8">
                    <FiAwardIcon className="text-4xl text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500 text-base">No certificates uploaded yet</p>
                    <p className="text-gray-400 text-sm mt-1">Upload your first certificate to get started</p>
                  </div>
                ) : (
                  certificates.map((certificate) => (
                    <div key={certificate.id} className="bg-gray-50 rounded-lg p-3 border-2 border-black hover:shadow-lg transition-all duration-200">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold border border-white shadow-lg">
                              {certificate.issueDate}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded border border-black ${
                              certificate.status === 'Issued' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                            }`}>
                              {certificate.status}
                            </span>
                          </div>
                          <h4 className="text-base font-bold text-gray-900 mb-2">{certificate.name}</h4>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="flex items-center text-gray-700 bg-gray-100 p-1.5 rounded border border-black">
                              <FiUser className="mr-1 text-blue-500 text-xs" />
                              <span className="font-medium text-xs">{certificate.student}</span>
                            </div>
                            <div className="flex items-center text-gray-700 bg-gray-100 p-1.5 rounded border border-black">
                              <FiFile className="mr-1 text-green-500 text-xs" />
                              <span className="font-medium text-xs">{certificate.file}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-1 ml-2">
                          <button className="bg-blue-600 text-white p-1 rounded hover:bg-blue-700 transition-colors border border-blue-600">
                            <FiEye className="text-xs" />
                          </button>
                          <button className="bg-green-600 text-white p-1 rounded hover:bg-green-700 transition-colors border border-green-600">
                            <FiEdit2 className="text-xs" />
                          </button>
                          <button className="bg-red-600 text-white p-1 rounded hover:bg-red-700 transition-colors border border-red-600">
                            <FiTrash2 className="text-xs" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard; 