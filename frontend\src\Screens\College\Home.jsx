import React from "react";
import CollegeLayout from "../../components/Layout/CollegeLayout";
import { FiUsers, FiBook, FiUserCheck, FiGrid, FiInfo, FiCalendar, FiDownload, FiArrowRight } from "react-icons/fi";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";

const quickStats = [
  { label: "Departments", value: 8, icon: <FiGrid className="text-xl text-blue-500" />, color: "bg-blue-50" },
  { label: "Students", value: 1200, icon: <FiUsers className="text-xl text-green-500" />, color: "bg-green-50" },
  { label: "Faculty", value: 85, icon: <FiUserCheck className="text-xl text-purple-500" />, color: "bg-purple-50" },
  { label: "Courses", value: 42, icon: <FiBook className="text-xl text-orange-500" />, color: "bg-orange-50" },
];

const announcements = [
  { id: 1, title: "NAAC Peer Team Visit", date: "2024-07-10", desc: "The NAAC peer team will visit the campus for assessment.", type: "event" },
  { id: 2, title: "Faculty Development Program", date: "2024-07-15", desc: "A 3-day FDP on modern teaching methods.", type: "workshop" },
  { id: 3, title: "Semester Results Published", date: "2024-06-30", desc: "Results for all batches are now available for download.", type: "notice" },
];

const quickLinks = [
  { label: "Department", to: "/college/department", icon: <FiGrid className="text-xl" /> },
  { label: "CSE", to: "/college/cse", icon: <FiUsers className="text-xl" /> },
  { label: "AIML", to: "/college/aiml", icon: <FiUsers className="text-xl" /> },
  { label: "ISE", to: "/college/ise", icon: <FiUsers className="text-xl" /> },
  { label: "IOT", to: "/college/iot", icon: <FiUsers className="text-xl" /> },
  { label: "Civil", to: "/college/civil", icon: <FiUsers className="text-xl" /> },
  { label: "Mech", to: "/college/mech", icon: <FiUsers className="text-xl" /> },
  { label: "CSD", to: "/college/csd", icon: <FiUserCheck className="text-xl" /> },
];

const Home = () => {
  return (
    <CollegeLayout>
      <div className="w-full min-h-screen bg-gradient-to-b from-blue-100 via-white to-gray-50 py-6 px-2 sm:py-10 sm:px-2">
        <div className="max-w-6xl mx-auto">
          {/* Dashboard Header */}
          <div className="flex items-center justify-between mb-8 px-2 sm:px-0">
            <div className="flex items-center gap-3">
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-800">College Dashboard</h1>
                <p className="text-gray-500 text-sm sm:text-base mt-1">Welcome! Here’s your college overview and quick actions.</p>
              </div>
            </div>
            {/* College Logo/Avatar */}
            <div className="hidden sm:flex items-center gap-2">
              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-400 flex items-center justify-center text-white font-bold text-xl shadow">
                C
              </div>
              <span className="text-gray-700 font-semibold">College</span>
            </div>
          </div>
          {/* Quick Stats */}
          <motion.div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8"
            initial="hidden" animate="visible" variants={{
              hidden: {},
              visible: { transition: { staggerChildren: 0.15 } }
            }}
          >
            {quickStats.map((stat, idx) => (
              <motion.div
                key={idx}
                className={`flex flex-col items-center justify-center rounded-xl shadow p-5 border border-gray-100 ${stat.color}`}
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: idx * 0.1 }}
              >
                <div>{stat.icon}</div>
                <div className="text-2xl font-bold text-gray-800 mt-2">{stat.value}</div>
                <div className="text-sm text-gray-500 mt-1">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
          {/* Quick Links */}
          <div className="mb-8">
            <div className="flex items-center mb-2">
              <FiArrowRight className="text-blue-500 mr-2" />
              <h2 className="text-base sm:text-lg font-semibold text-gray-700">Quick Links</h2>
            </div>
            <motion.div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4"
              initial="hidden" animate="visible" variants={{
                hidden: {},
                visible: { transition: { staggerChildren: 0.08 } }
              }}
            >
              {quickLinks.map((link, idx) => (
                <motion.div
                  key={idx}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: idx * 0.07 }}
                >
                  <Link to={link.to} className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-5 border border-gray-100 hover:shadow-lg transition-all group">
                    <div className="mb-2 text-blue-600 group-hover:text-blue-800">{link.icon}</div>
                    <div className="text-sm font-semibold text-gray-700 group-hover:text-blue-800">{link.label}</div>
                  </Link>
                </motion.div>
              ))}
            </motion.div>
          </div>
          {/* Announcements/Notices */}
          <div className="mb-8">
            <div className="flex items-center mb-2">
              <FiInfo className="text-blue-500 mr-2" />
              <h2 className="text-base sm:text-lg font-semibold text-gray-700">Recent Announcements</h2>
            </div>
            <motion.div className="grid gap-4 sm:grid-cols-2"
              initial="hidden" animate="visible" variants={{
                hidden: {},
                visible: { transition: { staggerChildren: 0.12 } }
              }}
            >
              {announcements.map((a, idx) => (
                <motion.div
                  key={a.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: idx * 0.1 }}
                  className="bg-white rounded-xl shadow-md p-5 flex flex-col gap-2 border border-gray-100 hover:shadow-lg transition-all"
                >
                  <div className="flex items-center gap-2 mb-1">
                    {a.type === "event" && <FiCalendar className="text-blue-500 text-xl" />}
                    {a.type === "workshop" && <FiBook className="text-green-500 text-xl" />}
                    {a.type === "notice" && <FiInfo className="text-purple-500 text-xl" />}
                    <span className="font-semibold text-gray-800 text-lg">{a.title}</span>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-gray-400 mb-1">
                    <FiCalendar className="text-gray-300" />
                    <span>{a.date}</span>
                  </div>
                  <div className="text-sm text-gray-600 border-t border-gray-100 pt-2">{a.desc}</div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </CollegeLayout>
  );
};

export default Home; 