import React, { useState, useEffect } from 'react';
import { 
  FiHome, 
  FiMapPin, 
  FiHash, 
  FiMail, 
  FiPlus, 
  FiEdit2, 
  FiTrash2, 
  FiEye,
  FiX,
  FiSave,
  FiRefreshCw,
  FiBriefcase
} from 'react-icons/fi';


const College = () => {
  const [name, setName] = useState('');
  const [location, setLocation] = useState('');
  const [code, setCode] = useState('');
  const [adminEmail, setAdminEmail] = useState('');
  const [colleges, setColleges] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedCollege, setSelectedCollege] = useState(null);
  const [isEditing, setIsEditing] = useState(false);



  const handleSubmit = (e) => {
    e.preventDefault();
    const newCollege = {
      id: Date.now(),
      name,
      location,
      contact: adminEmail,
      departments: 0,
    };
    
    // Add college to local state
    setColleges([...colleges, newCollege]);
    handleReset();
  };

  const handleReset = () => {
    setName('');
    setLocation('');
    setCode('');
    setAdminEmail('');
  };

  const openCollegeModal = (college) => {
    setSelectedCollege(college);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedCollege(null);
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const handleDeleteCollege = (id) => {
    setColleges(colleges.filter(college => college.id !== id));
  };

  return (
    <div className=" p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Manage Colleges</h1>
            <p className="text-gray-700 mt-1">Add and manage college information</p>
          </div>
          <button 
            onClick={handleEditToggle}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
              isEditing 
                ? "bg-red-600 text-white hover:bg-red-700" 
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
          >
            {isEditing ? <FiX size={16} /> : <FiPlus size={16} />}
            {isEditing ? "Cancel" : "Edit Mode"}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - College Creation Form */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Add New College</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">College Name</label>
                  <input 
                    value={name} 
                    onChange={e => setName(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="College Name" 
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Location</label>
                  <input 
                    value={location} 
                    onChange={e => setLocation(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="Location" 
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">College Code/ID</label>
                  <input 
                    value={code} 
                    onChange={e => setCode(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="College Code/ID" 
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Admin Email</label>
                  <input 
                    value={adminEmail} 
                    onChange={e => setAdminEmail(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="Admin Email" 
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <button 
                    type="submit" 
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiSave className="mr-2" />
                    Add College
                  </button>
                  <button 
                    type="button" 
                    onClick={handleReset} 
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiRefreshCw className="mr-2" />
                    Reset
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Right Panel - College List */}
          <div className="lg:col-span-2">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
              <div className="border-b border-gray-200">
                <div className="flex space-x-8 px-6">
                  <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-blue-600 font-medium text-sm text-blue-600">
                    <FiBriefcase size={16} />
                    All Colleges
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {colleges.length === 0 ? (
                    <div className="text-center py-12">
                      <FiBriefcase className="text-6xl text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500 text-lg">No colleges added yet</p>
                      <p className="text-gray-400 text-sm mt-2">Add your first college using the form</p>
                    </div>
                  ) : (
                    colleges.map((college) => (
                      <div key={college.id} className="bg-gray-50 rounded-xl p-6 border-2 border-black hover:shadow-lg transition-all duration-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-4">
                              <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white shadow-lg">
                                {college.departments} Depts
                              </span>
                              <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                                {college.location}
                              </span>
                            </div>
                            <h4 className="text-xl font-bold text-gray-900 mb-3">{college.name}</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiMapPin className="mr-3 text-green-500" />
                                <span className="font-medium">{college.location}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiMail className="mr-3 text-blue-500" />
                                <span className="font-medium">{college.contact}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <button
                              onClick={() => openCollegeModal(college)}
                              className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors border-2 border-blue-600"
                            >
                              <FiEye className="text-lg" />
                            </button>
                            <button
                              className="bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors border-2 border-green-600"
                            >
                              <FiEdit2 className="text-lg" />
                            </button>
                            <button
                              onClick={() => handleDeleteCollege(college.id)}
                              className="bg-red-600 text-white p-2 rounded-lg hover:bg-red-700 transition-colors border-2 border-red-600"
                            >
                              <FiTrash2 className="text-lg" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* College Details Modal */}
      {showModal && selectedCollege && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 border-2 border-black">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">College Details</h3>
              <button
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700 text-2xl"
              >
                ×
              </button>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white">
                  {selectedCollege.departments} Depts
                </span>
                <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                  {selectedCollege.location}
                </span>
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4">{selectedCollege.name}</h4>
              <div className="space-y-3">
                <div className="flex items-center text-gray-700">
                  <FiMapPin className="mr-3 text-green-500" />
                  <span className="font-medium">{selectedCollege.location}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiMail className="mr-3 text-blue-500" />
                  <span className="font-medium">{selectedCollege.contact}</span>
                </div>
                                 <div className="flex items-center text-gray-700">
                   <FiBriefcase className="mr-3 text-orange-500" />
                   <span className="font-medium">Active Institution</span>
                 </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default College; 