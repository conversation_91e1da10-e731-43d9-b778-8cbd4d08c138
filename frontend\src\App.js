import React from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { Provider } from "react-redux";
import { ThemeProvider } from 'styled-components';
import { theme } from './theme';
import Login from "./components/Login";
import mystore from "./redux/store";
import AdminRoutes from "./routes/AdminRoutes";
import StudentRoutes from "./routes/StudentRoutes";
import FacultyHome from "./Screens/Faculty/Home";
import CollegeRoutes from "./routes/CollegeRoutes";

const App = () => {
  return (
    <ThemeProvider theme={theme}>
      <Provider store={mystore}>
        <Router>
          <Routes>
            <Route path="/" element={<Login />} />
            <Route path="/login" element={<Navigate to="/" replace />} />
            
            {/* Admin Routes */}
            <Route path="/admin/*" element={<AdminRoutes />} />
            
            {/* Student Routes */}
            <Route path="/student/*" element={<StudentRoutes />} />
            
            {/* Faculty Routes */}
            <Route path="/faculty/*" element={<FacultyHome />} />
            
            {/* College Routes */}
            <Route path="/college/*" element={<CollegeRoutes />} />
            
            {/* Fallback route */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Router>
      </Provider>
    </ThemeProvider>
  );
};

export default App;
