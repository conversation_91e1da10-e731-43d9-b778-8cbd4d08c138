import React, { useState } from 'react';
import {
  <PERSON>Home,
  FiUser,
  FiLayers,
  FiBook,
  FiCalendar,
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiEye,
  FiX,
  FiSave,
  FiRefreshCw,
  FiUsers,
  FiMapPin,
  FiClock
} from 'react-icons/fi';

const collegeOptions = ['Medini College', 'Tech University', 'Engineering Institute', 'Science Academy'];
const trainerOptions = {
  'Medini College': ['<PERSON>', '<PERSON>', '<PERSON>'],
  'Tech University': ['<PERSON>', '<PERSON>', '<PERSON>'],
  'Engineering Institute': ['<PERSON>', '<PERSON>', '<PERSON>'],
  'Science Academy': ['<PERSON>', '<PERSON>', '<PERSON>']
};
const batchOptions = ['Batch A – 2024', 'Batch B – 2025', 'Batch C – 2026', 'Batch D – 2027'];
const courseOptions = ['Web Development', 'Data Science', 'Machine Learning', 'Mobile Development', 'Cloud Computing'];

const initialAssignments = [
  {
    id: 1,
    college: 'Medini College',
    trainer: '<PERSON>',
    batch: 'Batch A – 2024',
    course: 'Web Development',
    duration: '2024-01-01 to 2024-12-31',
  },
];

const AssignBatchToTrainers = () => {
  const [college, setCollege] = useState('');
  const [trainer, setTrainer] = useState('');
  const [batch, setBatch] = useState('');
  const [course, setCourse] = useState('');
  const [assignFrom, setAssignFrom] = useState('');
  const [assignTo, setAssignTo] = useState('');
  const [assignments, setAssignments] = useState(initialAssignments);
  const [showModal, setShowModal] = useState(false);
  const [selectedAssignment, setSelectedAssignment] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    const duration = `${assignFrom} to ${assignTo}`;
    const newAssignment = {
      id: Date.now(),
      college,
      trainer,
      batch,
      course,
      duration,
    };
    setAssignments([...assignments, newAssignment]);
    handleReset();
  };

  const handleReset = () => {
    setCollege('');
    setTrainer('');
    setBatch('');
    setCourse('');
    setAssignFrom('');
    setAssignTo('');
  };

  const openAssignmentModal = (assignment) => {
    setSelectedAssignment(assignment);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedAssignment(null);
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  // Reset trainer when college changes
  const handleCollegeChange = (e) => {
    setCollege(e.target.value);
    setTrainer('');
  };

  return (
    <div className=" p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Assign Batch to Trainers</h1>
            <p className="text-gray-700 mt-1">Manage batch assignments and trainer allocations</p>
          </div>
          <button
            onClick={handleEditToggle}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
              isEditing
                ? "bg-red-600 text-white hover:bg-red-700"
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
          >
            {isEditing ? <FiX size={16} /> : <FiPlus size={16} />}
            {isEditing ? "Cancel" : "Edit Mode"}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Assignment Creation Form */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Create New Assignment</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiHome className="mr-2 text-red-500" />
                    Select College
                  </label>
                  <select
                    value={college}
                    onChange={handleCollegeChange}
                    required
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select College</option>
                    {collegeOptions.map(c => <option key={c} value={c}>{c}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiUser className="mr-2 text-yellow-500" />
                    Select Trainer
                  </label>
                  <select
                    value={trainer}
                    onChange={e => setTrainer(e.target.value)}
                    required
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={!college}
                  >
                    <option value="">Select Trainer</option>
                    {college && trainerOptions[college]?.map(t => <option key={t} value={t}>{t}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiLayers className="mr-2 text-blue-500" />
                    Select Batch
                  </label>
                  <select
                    value={batch}
                    onChange={e => setBatch(e.target.value)}
                    required
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Batch</option>
                    {batchOptions.map(b => <option key={b} value={b}>{b}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiBook className="mr-2 text-green-500" />
                    Select Course
                  </label>
                  <select
                    value={course}
                    onChange={e => setCourse(e.target.value)}
                    required
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Course</option>
                    {courseOptions.map(c => <option key={c} value={c}>{c}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiCalendar className="mr-2 text-purple-500" />
                    Assign From
                  </label>
                  <input
                    type="date"
                    value={assignFrom}
                    onChange={e => setAssignFrom(e.target.value)}
                    required
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiCalendar className="mr-2 text-purple-500" />
                    Assign To
                  </label>
                  <input
                    type="date"
                    value={assignTo}
                    onChange={e => setAssignTo(e.target.value)}
                    required
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <button
                    type="submit"
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiSave className="mr-2" />
                    Assign
                  </button>
                  <button
                    type="button"
                    onClick={handleReset}
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiRefreshCw className="mr-2" />
                    Reset
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Right Panel - Assignment List */}
          <div className="lg:col-span-2">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
              <div className="border-b border-gray-200">
                <div className="flex space-x-8 px-6">
                  <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-blue-600 font-medium text-sm text-blue-600">
                    <FiUsers size={16} />
                    All Assignments
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {assignments.length === 0 ? (
                    <div className="text-center py-12">
                      <FiUsers className="text-6xl text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500 text-lg">No assignments created yet</p>
                      <p className="text-gray-400 text-sm mt-2">Create your first assignment using the form</p>
                    </div>
                  ) : (
                    assignments.map((assignment) => (
                      <div key={assignment.id} className="bg-gray-50 rounded-xl p-6 border-2 border-black hover:shadow-lg transition-all duration-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-4">
                              <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white shadow-lg">
                                {assignment.batch}
                              </span>
                              <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                                {assignment.course}
                              </span>
                            </div>
                            <h4 className="text-xl font-bold text-gray-900 mb-3">{assignment.college}</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiUser className="mr-3 text-yellow-500" />
                                <span className="font-medium">{assignment.trainer}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiMapPin className="mr-3 text-green-500" />
                                <span className="font-medium">{assignment.college}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black md:col-span-2">
                                <FiClock className="mr-3 text-purple-500" />
                                <span className="font-medium">{assignment.duration}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <button
                              onClick={() => openAssignmentModal(assignment)}
                              className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors border-2 border-blue-600"
                            >
                              <FiEye className="text-lg" />
                            </button>
                            <button
                              className="bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors border-2 border-green-600"
                            >
                              <FiEdit2 className="text-lg" />
                            </button>
                            <button
                              className="bg-red-600 text-white p-2 rounded-lg hover:bg-red-700 transition-colors border-2 border-red-600"
                            >
                              <FiTrash2 className="text-lg" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Assignment Details Modal */}
      {showModal && selectedAssignment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 border-2 border-black">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Assignment Details</h3>
              <button
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700 text-2xl"
              >
                ×
              </button>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white">
                  {selectedAssignment.batch}
                </span>
                <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                  {selectedAssignment.course}
                </span>
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4">{selectedAssignment.college}</h4>
              <div className="space-y-3">
                <div className="flex items-center text-gray-700">
                  <FiUser className="mr-3 text-yellow-500" />
                  <span className="font-medium">{selectedAssignment.trainer}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiMapPin className="mr-3 text-green-500" />
                  <span className="font-medium">{selectedAssignment.college}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiLayers className="mr-3 text-blue-500" />
                  <span className="font-medium">{selectedAssignment.batch}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiBook className="mr-3 text-green-500" />
                  <span className="font-medium">{selectedAssignment.course}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiClock className="mr-3 text-purple-500" />
                  <span className="font-medium">{selectedAssignment.duration}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssignBatchToTrainers; 