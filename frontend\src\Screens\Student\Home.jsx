import React, { useState } from "react";
import StudentLayout from "../../components/Layout/StudentLayout";
import { <PERSON>Book, FiCheckCircle, FiCalendar, FiUser, FiArrowRight, FiInfo, FiGrid, FiTrendingUp, FiEdit3, FiBookOpen, FiAward, FiEdit2, FiUsers, FiFileText, FiClock, FiTarget, FiCheckSquare, FiEye, FiEdit, FiDownload, FiTrash2 } from "react-icons/fi";
import { Link } from "react-router-dom";
import { Toaster } from "react-hot-toast";

// Mock data for active courses, assessments, and assignments
const activeCourses = [
  { id: 1, name: "Advanced Java Programming", instructor: "<PERSON><PERSON> <PERSON>", progress: 75, totalClasses: 20, attendedClasses: 15 },
  { id: 2, name: "Data Structures & Algorithms", instructor: "<PERSON><PERSON>", progress: 60, totalClasses: 25, attendedClasses: 15 },
  { id: 3, name: "Database Management", instructor: "<PERSON><PERSON>", progress: 85, totalClasses: 18, attendedClasses: 15 },
  { id: 4, name: "Web Development", instructor: "<PERSON><PERSON>", progress: 90, totalClasses: 15, attendedClasses: 13 }
];

const activeAssessments = [
  { id: 1, subject: "Advanced Java", type: "Midterm", date: "2024-04-15", status: "Upcoming", marks: null, maxMarks: 50 },
  { id: 2, subject: "Data Structures", type: "Quiz", date: "2024-04-10", status: "Completed", marks: 42, maxMarks: 50 },
  { id: 3, subject: "Database", type: "Assignment", date: "2024-04-08", status: "Completed", marks: 18, maxMarks: 20 },
  { id: 4, subject: "Web Development", type: "Project", date: "2024-04-20", status: "Upcoming", marks: null, maxMarks: 100 }
];

const activeAssignments = [
  { id: 1, subject: "Advanced Java", title: "Spring Boot Project", dueDate: "2024-04-12", status: "Submitted", priority: "High" },
  { id: 2, subject: "Data Structures", title: "Binary Tree Implementation", dueDate: "2024-04-15", status: "Pending", priority: "Medium" },
  { id: 3, subject: "Database", title: "SQL Queries Assignment", dueDate: "2024-04-10", status: "Submitted", priority: "Low" },
  { id: 4, subject: "Web Development", title: "React Portfolio", dueDate: "2024-04-18", status: "Pending", priority: "High" }
];

// Course list data for the table
const courseList = [
  { id: 1, courseTitle: "AutoCAD", endDate: "2024-05-15", attendance: "85%", status: "Active" },
  { id: 2, courseTitle: "Full Stack Java", endDate: "2024-06-20", attendance: "92%", status: "Active" },
  { id: 3, courseTitle: "MERN Stack", endDate: "2024-07-10", attendance: "78%", status: "Active" },
  { id: 4, courseTitle: "Data Science", endDate: "2024-08-05", attendance: "88%", status: "Active" },
  { id: 5, courseTitle: "Machine Learning", endDate: "2024-09-15", attendance: "95%", status: "Active" }
];

const quickLinks = [
  { label: "My Courses", to: "/student/courses", icon: <FiBook className="text-xl" /> },
  { label: "Schedule", to: "/student/schedule", icon: <FiCalendar className="text-xl" /> },
  { label: "Attendance", to: "/student/attendance", icon: <FiCheckCircle className="text-xl" /> },
  { label: "Profile", to: "/student/profile", icon: <FiUser className="text-xl" /> }
];

const Home = () => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-700 border-green-200';
      case 'Upcoming': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'Pending': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'Submitted': return 'bg-green-100 text-green-700 border-green-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'High': return 'bg-red-100 text-red-700 border-red-200';
      case 'Medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'Low': return 'bg-green-100 text-green-700 border-green-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getProgressColor = (progress) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getAttendanceColor = (attendance) => {
    const percentage = parseInt(attendance);
    if (percentage >= 90) return 'text-green-600 font-semibold';
    if (percentage >= 75) return 'text-yellow-600 font-semibold';
    return 'text-red-600 font-semibold';
  };

  return (
    <StudentLayout>
      <div className="w-full min-h-screen bg-white">
        <div className="max-w-7xl mx-auto p-6">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-white p-3 rounded-full shadow-lg border-2 border-black">
                  <FiGrid className="text-3xl text-blue-600" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Student Dashboard</h1>
                  <p className="text-gray-600 mt-2">Welcome back! Here's your academic overview.</p>
                </div>
              </div>
              <div className="hidden sm:flex items-center gap-2">
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-400 flex items-center justify-center text-white font-bold text-xl shadow-lg border-2 border-black">
                  S
                </div>
                <span className="text-gray-700 font-semibold">Student</span>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="bg-white rounded-lg shadow-sm border-2 border-black p-6">
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Active Courses Summary */}
                <div className="bg-blue-50 p-6 rounded-lg border-2 border-blue-200">
                  <div className="flex items-center mb-4">
                    <FiBook className="text-blue-600 text-2xl" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-blue-600">Active Courses</p>
                      <p className="text-2xl font-bold text-blue-900">{activeCourses.length}</p>
                    </div>
                  </div>
                  <div className="text-sm text-blue-700">
                    <p>Average Progress: {Math.round(activeCourses.reduce((sum, course) => sum + course.progress, 0) / activeCourses.length)}%</p>
                  </div>
                </div>

                {/* Active Assessments Summary */}
                <div className="bg-green-50 p-6 rounded-lg border-2 border-green-200">
                  <div className="flex items-center mb-4">
                    <FiTarget className="text-green-600 text-2xl" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-green-600">Active Assessments</p>
                      <p className="text-2xl font-bold text-green-900">{activeAssessments.length}</p>
                    </div>
                  </div>
                  <div className="text-sm text-green-700">
                    <p>Completed: {activeAssessments.filter(a => a.status === 'Completed').length}</p>
                  </div>
                </div>

                {/* Active Assignments Summary */}
                <div className="bg-purple-50 p-6 rounded-lg border-2 border-purple-200">
                  <div className="flex items-center mb-4">
                    <FiFileText className="text-purple-600 text-2xl" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-purple-600">Active Assignments</p>
                      <p className="text-2xl font-bold text-purple-900">{activeAssignments.length}</p>
                    </div>
                  </div>
                  <div className="text-sm text-purple-700">
                    <p>Pending: {activeAssignments.filter(a => a.status === 'Pending').length}</p>
                  </div>
                </div>
              </div>

              {/* Course List Section */}
              <div className="mt-8">
                <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
                  <FiBook className="text-blue-500" />
                  My Course List
                </h3>
                <div className="bg-white rounded-lg border-2 border-black overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50 border-b-2 border-black">
                        <tr>
                          <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-r-2 border-black">No.</th>
                          <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-r-2 border-black">Course Title</th>
                          <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-r-2 border-black">End Date</th>
                          <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-r-2 border-black">Attendance</th>
                          <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {courseList.map((course, index) => (
                          <tr key={course.id} className="border-b border-gray-200 hover:bg-gray-50">
                            <td className="px-6 py-4 text-sm text-gray-900 border-r-2 border-black font-medium">
                              {index + 1}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900 border-r-2 border-black font-medium">
                              {course.courseTitle}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-600 border-r-2 border-black">
                              {course.endDate}
                            </td>
                            <td className="px-6 py-4 text-sm border-r-2 border-black">
                              <span className={getAttendanceColor(course.attendance)}>
                                {course.attendance}
                              </span>
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-600">
                              <button className="text-blue-600 hover:text-blue-800 font-medium hover:underline">
                                View
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <FiArrowRight className="text-blue-500" />
                  Quick Links
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                  {quickLinks.map((link, idx) => (
                    <Link
                      to={link.to}
                      key={idx}
                      className="flex flex-col items-center justify-center bg-gray-50 rounded-lg shadow-lg border-2 border-black hover:shadow-xl p-4 transition-all duration-200 group hover:scale-105 cursor-pointer"
                    >
                      <div className="mb-2 text-blue-600 group-hover:text-blue-800">{link.icon}</div>
                      <div className="font-semibold text-gray-700 text-sm">{link.label}</div>
                      <FiArrowRight className="text-gray-400 group-hover:text-blue-600 mt-2" />
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Toaster position="bottom-center" />
    </StudentLayout>
  );
};

export default Home;
