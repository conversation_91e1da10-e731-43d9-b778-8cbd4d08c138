// Mock data service to replace backend API calls

// Mock user data
const mockUsers = {
  students: [
    {
      loginid: "1001",
      password: "student123",
      name: "<PERSON>",
      email: "<EMAIL>",
      branch: "Computer Science",
      year: "3rd Year",
      phone: "1234567890"
    },
    {
      loginid: "1002", 
      password: "student123",
      name: "<PERSON>",
      email: "<EMAIL>",
      branch: "Information Technology",
      year: "2nd Year",
      phone: "9876543210"
    }
  ],
  faculty: [
    {
      loginid: "2001",
      password: "faculty123",
      name: "Dr. <PERSON>",
      email: "<EMAIL>",
      branch: "Computer Science",
      phone: "5551234567"
    },
    {
      loginid: "2002",
      password: "faculty123", 
      name: "Prof. <PERSON>",
      email: "<EMAIL>",
      branch: "Information Technology",
      phone: "5559876543"
    }
  ],
  admins: [
    {
      loginid: "806",
      password: "0806",
      name: "<PERSON><PERSON> User",
      email: "<EMAIL>",
      phone: "5550000000"
    }
  ],
  colleges: [
    {
      loginid: "college001",
      password: "password123",
      name: "Medini College",
      email: "<EMAIL>",
      phone: "5551112222"
    }
  ]
};

// Mock academic data
const mockData = {
  branches: [
    { id: 1, name: "Computer Science" },
    { id: 2, name: "Information Technology" },
    { id: 3, name: "Electronics" },
    { id: 4, name: "Mechanical" }
  ],
  subjects: [
    { id: 1, name: "Data Structures", branch: "Computer Science" },
    { id: 2, name: "Database Systems", branch: "Computer Science" },
    { id: 3, name: "Web Development", branch: "Information Technology" },
    { id: 4, name: "Network Security", branch: "Information Technology" }
  ],
  notices: [
    {
      id: 1,
      title: "Exam Schedule Update",
      content: "The final examination schedule has been updated. Please check the new dates.",
      date: "2024-01-15",
      author: "Admin"
    },
    {
      id: 2,
      title: "Holiday Notice",
      content: "College will be closed on Republic Day, January 26th, 2024.",
      date: "2024-01-20",
      author: "Admin"
    }
  ],
  materials: [
    {
      id: 1,
      title: "Data Structures Notes",
      subject: "Data Structures",
      filename: "ds_notes.pdf",
      uploadDate: "2024-01-10"
    },
    {
      id: 2,
      title: "Database Lab Manual",
      subject: "Database Systems", 
      filename: "db_lab.pdf",
      uploadDate: "2024-01-12"
    }
  ],
  timetables: [
    {
      id: 1,
      day: "Monday",
      subject: "Data Structures",
      time: "9:00 AM - 10:00 AM",
      room: "Lab 101"
    },
    {
      id: 2,
      day: "Tuesday",
      subject: "Database Systems",
      time: "10:00 AM - 11:00 AM", 
      room: "Lab 102"
    }
  ],
  marks: [
    {
      studentId: "1001",
      subject: "Data Structures",
      internal: 85,
      external: 78,
      total: 81.5
    },
    {
      studentId: "1002",
      subject: "Web Development", 
      internal: 90,
      external: 85,
      total: 87.5
    }
  ]
};

// Authentication service
export const authService = {
  login: (loginid, password, userType) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        let users;
        if (userType.toLowerCase() === 'college') {
          users = mockUsers.colleges;
        } else {
          users = mockUsers[userType.toLowerCase() + 's'];
        }
        const user = users && users.find(u => u.loginid === loginid && u.password === password);
        
        if (user) {
          resolve({
            success: true,
            loginid: user.loginid,
            user: user
          });
        } else {
          reject({ message: "Invalid credentials" });
        }
      }, 500);
    });
  },

  updatePassword: (loginid, oldPassword, newPassword, userType) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const users = mockUsers[userType.toLowerCase() + 's'];
        const userIndex = users.findIndex(u => u.loginid === loginid && u.password === oldPassword);
        
        if (userIndex !== -1) {
          users[userIndex].password = newPassword;
          resolve({ success: true, message: "Password updated successfully" });
        } else {
          reject({ message: "Invalid credentials" });
        }
      }, 500);
    });
  }
};

// Data service
export const dataService = {
  // User management
  getUsers: (userType) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockUsers[userType.toLowerCase() + 's']);
      }, 300);
    });
  },

  addUser: (userData, userType) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const users = mockUsers[userType.toLowerCase() + 's'];
        const newUser = {
          ...userData,
          id: Date.now()
        };
        users.push(newUser);
        resolve({ success: true, user: newUser });
      }, 300);
    });
  },

  updateUser: (id, userData, userType) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const users = mockUsers[userType.toLowerCase() + 's'];
        const userIndex = users.findIndex(u => u.loginid === id);
        if (userIndex !== -1) {
          users[userIndex] = { ...users[userIndex], ...userData };
          resolve({ success: true, user: users[userIndex] });
        }
      }, 300);
    });
  },

  deleteUser: (id, userType) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const users = mockUsers[userType.toLowerCase() + 's'];
        const userIndex = users.findIndex(u => u.loginid === id);
        if (userIndex !== -1) {
          users.splice(userIndex, 1);
          resolve({ success: true });
        }
      }, 300);
    });
  },

  // Academic data
  getBranches: () => {
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockData.branches), 300);
    });
  },

  addBranch: (branchData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newBranch = {
          id: Date.now(),
          ...branchData
        };
        mockData.branches.push(newBranch);
        resolve({ success: true, branch: newBranch });
      }, 300);
    });
  },

  deleteBranch: (id) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const branchIndex = mockData.branches.findIndex(b => b.id === id);
        if (branchIndex !== -1) {
          mockData.branches.splice(branchIndex, 1);
          resolve({ success: true });
        }
      }, 300);
    });
  },

  getSubjects: () => {
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockData.subjects), 300);
    });
  },

  addSubject: (subjectData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newSubject = {
          id: Date.now(),
          ...subjectData
        };
        mockData.subjects.push(newSubject);
        resolve({ success: true, subject: newSubject });
      }, 300);
    });
  },

  deleteSubject: (id) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const subjectIndex = mockData.subjects.findIndex(s => s.id === id);
        if (subjectIndex !== -1) {
          mockData.subjects.splice(subjectIndex, 1);
          resolve({ success: true });
        }
      }, 300);
    });
  },

  // Notices
  getNotices: () => {
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockData.notices), 300);
    });
  },

  addNotice: (noticeData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newNotice = {
          id: Date.now(),
          ...noticeData,
          date: new Date().toISOString().split('T')[0]
        };
        mockData.notices.push(newNotice);
        resolve({ success: true, notice: newNotice });
      }, 300);
    });
  },

  updateNotice: (id, noticeData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const noticeIndex = mockData.notices.findIndex(n => n.id === id);
        if (noticeIndex !== -1) {
          mockData.notices[noticeIndex] = { ...mockData.notices[noticeIndex], ...noticeData };
          resolve({ success: true, notice: mockData.notices[noticeIndex] });
        }
      }, 300);
    });
  },

  deleteNotice: (id) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const noticeIndex = mockData.notices.findIndex(n => n.id === id);
        if (noticeIndex !== -1) {
          mockData.notices.splice(noticeIndex, 1);
          resolve({ success: true });
        }
      }, 300);
    });
  },

  // Materials
  getMaterials: () => {
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockData.materials), 300);
    });
  },

  addMaterial: (materialData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newMaterial = {
          id: Date.now(),
          ...materialData,
          uploadDate: new Date().toISOString().split('T')[0]
        };
        mockData.materials.push(newMaterial);
        resolve({ success: true, material: newMaterial });
      }, 300);
    });
  },

  // Timetables
  getTimetables: () => {
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockData.timetables), 300);
    });
  },

  addTimetable: (timetableData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newTimetable = {
          id: Date.now(),
          ...timetableData
        };
        mockData.timetables.push(newTimetable);
        resolve({ success: true, timetable: newTimetable });
      }, 300);
    });
  },

  // Marks
  getMarks: () => {
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockData.marks), 300);
    });
  },

  addMarks: (marksData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newMarks = {
          id: Date.now(),
          ...marksData,
          total: (parseFloat(marksData.internal) + parseFloat(marksData.external)) / 2
        };
        mockData.marks.push(newMarks);
        resolve({ success: true, marks: newMarks });
      }, 300);
    });
  },

  // Counts for dashboard
  getCounts: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          students: mockUsers.students.length,
          faculty: mockUsers.faculty.length,
          admins: mockUsers.admins.length
        });
      }, 300);
    });
  }
}; 