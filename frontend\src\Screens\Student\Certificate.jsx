import React, { useState } from "react";
import { FiA<PERSON>, FiExternalLink } from "react-icons/fi";

const defaultCertificates = [
  {
    id: "autodesk",
    name: "Autodesk Certified User",
    issuer: "Autodesk",
    date: "2024-06-01",
    description: "This certificate verifies that the student has successfully completed the Autodesk Certified User program.",
    image: null
  }
];

const Certificate = () => {
  const [selected, setSelected] = useState(null);
  
  return (
    <div className="max-w-3xl mx-auto mt-10 bg-white rounded-xl shadow p-8">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">Certificates</h1>
      <div className="space-y-6">
        {defaultCertificates.map((cert) => (
          <div key={cert.id} className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-6 shadow-sm">
            <div>
              <div className="text-lg font-semibold text-gray-800">{cert.name}</div>
              <div className="text-sm text-gray-500">Issued by <span className="font-medium text-blue-700">{cert.issuer}</span></div>
              <div className="text-xs text-gray-400 mt-1">Date: {cert.date}</div>
            </div>
            <button
              onClick={() => setSelected(cert)}
              className="inline-flex items-center gap-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded font-semibold text-sm transition"
            >
              <FiExternalLink /> View Certificate
            </button>
          </div>
        ))}
      </div>
      {selected && (
        <div className="mt-10 border-t pt-8">
          <div className="text-2xl font-bold text-gray-800 mb-2">{selected.name}</div>
          <div className="text-sm text-gray-500 mb-1">Issued by <span className="font-medium text-blue-700">{selected.issuer}</span></div>
          <div className="text-xs text-gray-400 mb-4">Date: {selected.date}</div>
          <div className="mb-6 text-gray-700 text-base">{selected.description}</div>
          <div className="w-full h-64 bg-gray-100 rounded flex items-center justify-center text-gray-400">
            Certificate preview coming soon
          </div>
        </div>
      )}
    </div>
  );
};

export default Certificate; 