import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  FiCalendar, 
  FiBookOpen, 
  FiUser, 
  FiPlus, 
  FiEdit2, 
  FiTrash2, 
  FiEye,
  FiX,
  FiSave,
  FiRefreshCw
} from 'react-icons/fi';


const departments = ['CS', 'ECE', 'MECH'];
const facultyOptions = ['ABHISHEK', 'KIRAN', 'MANOJ'];
const trainerOptions = ['Trainer A', 'Trainer B'];
const studentOptions = ['SHAKEABHI', 'RANKIR', 'NO NAME'];

const CreateBatch = () => {
  const [batchName, setBatchName] = useState('');
  const [department, setDepartment] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [faculty, setFaculty] = useState([]);
  const [trainer, setTrainer] = useState('');
  const [students, setStudents] = useState([]);
  const [batches, setBatches] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedBatch, setSelectedBatch] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  const handleFacultyChange = (e) => {
    const options = Array.from(e.target.selectedOptions, o => o.value);
    setFaculty(options);
  };

  const handleStudentChange = (e) => {
    const value = e.target.value;
    setStudents(prev =>
      prev.includes(value)
        ? prev.filter(s => s !== value)
        : [...prev, value]
    );
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const newBatch = {
      id: Date.now(),
      name: batchName,
      department,
      startDate,
      endDate,
      faculty,
      trainer,
      students: students.length,
    };
    
    // Add batch to local state
    setBatches([...batches, newBatch]);
    handleReset();
  };

  const handleReset = () => {
    setBatchName('');
    setDepartment('');
    setStartDate('');
    setEndDate('');
    setFaculty([]);
    setTrainer('');
    setStudents([]);
  };

  const openBatchModal = (batch) => {
    setSelectedBatch(batch);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedBatch(null);
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const handleDeleteBatch = (id) => {
    setBatches(batches.filter(batch => batch.id !== id));
  };

  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Create Batch</h1>
            <p className="text-gray-700 mt-1">Manage and create new batches for different departments</p>
          </div>
          <button 
            onClick={handleEditToggle}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
              isEditing 
                ? "bg-red-600 text-white hover:bg-red-700" 
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
          >
            {isEditing ? <FiX size={16} /> : <FiPlus size={16} />}
            {isEditing ? "Cancel" : "Edit Mode"}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Batch Creation Form */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Create New Batch</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Batch Name</label>
                  <input 
                    value={batchName} 
                    onChange={e => setBatchName(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="e.g., Batch A - 2025" 
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Department</label>
                  <select 
                    value={department} 
                    onChange={e => setDepartment(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Department</option>
                    {departments.map(dep => <option key={dep} value={dep}>{dep}</option>)}
                  </select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-2">Start Date</label>
                    <input 
                      type="date" 
                      value={startDate} 
                      onChange={e => setStartDate(e.target.value)} 
                      required 
                      className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    />
                  </div>
                  <div>
                    <label className="block text-gray-700 text-sm font-bold mb-2">End Date</label>
                    <input 
                      type="date" 
                      value={endDate} 
                      onChange={e => setEndDate(e.target.value)} 
                      required 
                      className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Assign Faculty</label>
                  <select 
                    multiple 
                    value={faculty} 
                    onChange={handleFacultyChange} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 h-28 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {facultyOptions.map(f => <option key={f} value={f}>{f}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Select Trainer</label>
                  <select 
                    value={trainer} 
                    onChange={e => setTrainer(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Trainer</option>
                    {trainerOptions.map(t => <option key={t} value={t}>{t}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Select Students</label>
                  <div className="bg-gray-50 border-2 border-black rounded-lg p-4 max-h-32 overflow-y-auto">
                    <div className="grid grid-cols-1 gap-2">
                      {studentOptions.map(s => (
                        <label key={s} className="flex items-center gap-2 text-gray-800 font-medium">
                          <input
                            type="checkbox"
                            value={s}
                            checked={students.includes(s)}
                            onChange={handleStudentChange}
                            className="accent-blue-600 w-4 h-4"
                          />
                          {s}
                        </label>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex gap-3 pt-4">
                  <button 
                    type="submit" 
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiSave className="mr-2" />
                    Create Batch
                  </button>
                  <button 
                    type="button" 
                    onClick={handleReset} 
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiRefreshCw className="mr-2" />
                    Reset
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Right Panel - Batch List */}
          <div className="lg:col-span-2">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
              <div className="border-b border-gray-200">
                <div className="flex space-x-8 px-6">
                  <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-blue-600 font-medium text-sm text-blue-600">
                    <FiUsers size={16} />
                    All Batches
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {batches.length === 0 ? (
                    <div className="text-center py-12">
                      <FiUsers className="text-6xl text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500 text-lg">No batches created yet</p>
                      <p className="text-gray-400 text-sm mt-2">Create your first batch using the form</p>
                    </div>
                  ) : (
                    batches.map((batch) => (
                      <div key={batch.id} className="bg-gray-50 rounded-xl p-6 border-2 border-black hover:shadow-lg transition-all duration-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-4">
                              <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white shadow-lg">
                                {batch.department}
                              </span>
                              <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                                {batch.students} Students
                              </span>
                            </div>
                            <h4 className="text-xl font-bold text-gray-900 mb-3">{batch.name}</h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiCalendar className="mr-3 text-blue-500" />
                                <span className="font-medium">{batch.startDate} - {batch.endDate}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiUser className="mr-3 text-green-500" />
                                <span className="font-medium">{batch.trainer}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiBookOpen className="mr-3 text-purple-500" />
                                <span className="font-medium">{batch.department}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <button
                              onClick={() => openBatchModal(batch)}
                              className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors border-2 border-blue-600"
                            >
                              <FiEye className="text-lg" />
                            </button>
                            <button
                              className="bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors border-2 border-green-600"
                            >
                              <FiEdit2 className="text-lg" />
                            </button>
                            <button
                              onClick={() => handleDeleteBatch(batch.id)}
                              className="bg-red-600 text-white p-2 rounded-lg hover:bg-red-700 transition-colors border-2 border-red-600"
                            >
                              <FiTrash2 className="text-lg" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Batch Details Modal */}
      {showModal && selectedBatch && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 border-2 border-black">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Batch Details</h3>
              <button
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700 text-2xl"
              >
                ×
              </button>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white">
                  {selectedBatch.department}
                </span>
                <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                  {selectedBatch.students} Students
                </span>
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4">{selectedBatch.name}</h4>
              <div className="space-y-3">
                <div className="flex items-center text-gray-700">
                  <FiCalendar className="mr-3 text-blue-500" />
                  <span className="font-medium">{selectedBatch.startDate} - {selectedBatch.endDate}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiUser className="mr-3 text-green-500" />
                  <span className="font-medium">{selectedBatch.trainer}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiBookOpen className="mr-3 text-purple-500" />
                  <span className="font-medium">{selectedBatch.department}</span>
                </div>

              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreateBatch; 