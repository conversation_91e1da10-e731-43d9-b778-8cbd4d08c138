import React, { useState, useRef, useEffect } from 'react';
import { 
  Fi<PERSON>ser, 
  FiMail, 
  FiPhone, 
  FiBookOpen, 
  FiAward, 
  FiPlus, 
  FiEdit2, 
  FiTrash2, 
  FiEye,
  FiX,
  FiSave,
  FiRefreshCw,
  FiImage
} from 'react-icons/fi';


const departmentOptions = ['CS', 'ECE', 'MECH'];
const expertiseOptions = ['AI', 'ML', 'Networks', 'DBMS'];

const Trainer = () => {
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [department, setDepartment] = useState('');
  const [expertise, setExpertise] = useState('');
  const [profilePic, setProfilePic] = useState('');
  const [trainers, setTrainers] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [selectedTrainer, setSelectedTrainer] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [preview, setPreview] = useState(null);
  const fileInputRef = useRef();



  const handleSubmit = (e) => {
    e.preventDefault();
    const newTrainer = {
      id: Date.now(),
      name,
      phone,
      email,
      specialization: expertise,
      profilePic: preview || profilePic,
      batches: 0,
    };
    
    // Add trainer to local state
    setTrainers([...trainers, newTrainer]);
    handleReset();
  };

  const handleReset = () => {
    setName('');
    setPhone('');
    setEmail('');
    setDepartment('');
    setExpertise('');
    setProfilePic('');
    setPreview(null);
  };

  const openTrainerModal = (trainer) => {
    setSelectedTrainer(trainer);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedTrainer(null);
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const handleProfilePicChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setPreview(URL.createObjectURL(file));
    }
  };

  const handleDeleteTrainer = (id) => {
    setTrainers(trainers.filter(trainer => trainer.id !== id));
  };

  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Manage Trainers</h1>
            <p className="text-gray-700 mt-1">Add and manage trainer information</p>
          </div>
          <button 
            onClick={handleEditToggle}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
              isEditing 
                ? "bg-red-600 text-white hover:bg-red-700" 
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
          >
            {isEditing ? <FiX size={16} /> : <FiPlus size={16} />}
            {isEditing ? "Cancel" : "Edit Mode"}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Trainer Creation Form */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Add New Trainer</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Name</label>
                  <input 
                    value={name} 
                    onChange={e => setName(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="Trainer Name" 
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Phone</label>
                  <input 
                    value={phone} 
                    onChange={e => setPhone(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="Phone Number" 
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Email</label>
                  <input 
                    value={email} 
                    onChange={e => setEmail(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="Email" 
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Department</label>
                  <select 
                    value={department} 
                    onChange={e => setDepartment(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Department</option>
                    {departmentOptions.map(dep => <option key={dep} value={dep}>{dep}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Expertise</label>
                  <select 
                    value={expertise} 
                    onChange={e => setExpertise(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Expertise</option>
                    {expertiseOptions.map(exp => <option key={exp} value={exp}>{exp}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2">Profile Picture</label>
                  <div className="flex items-center gap-4">
                    <div className="relative w-20 h-20">
                      <img
                        src={preview || "https://ui-avatars.com/api/?name=TR&background=3B82F6&color=ffffff&size=80"}
                        alt="Profile"
                        className="w-20 h-20 rounded-full object-cover border-4 border-blue-100"
                      />
                      <button
                        type="button"
                        onClick={() => fileInputRef.current.click()}
                        className="absolute bottom-0 right-0 bg-blue-600 text-white p-1 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
                        title="Change profile picture"
                      >
                        <FiImage size={12} />
                      </button>
                      <input
                        type="file"
                        accept="image/*"
                        ref={fileInputRef}
                        onChange={handleProfilePicChange}
                        className="hidden"
                      />
                    </div>
                    <div className="flex-1">
                      <input 
                        value={profilePic} 
                        onChange={e => setProfilePic(e.target.value)} 
                        className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                        placeholder="Profile Picture URL (optional)" 
                      />
                    </div>
                  </div>
                </div>

                <div className="flex gap-3 pt-4">
                  <button 
                    type="submit" 
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiSave className="mr-2" />
                    Add Trainer
                  </button>
                  <button 
                    type="button" 
                    onClick={handleReset} 
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiRefreshCw className="mr-2" />
                    Reset
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Right Panel - Trainer List */}
          <div className="lg:col-span-2">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
              <div className="border-b border-gray-200">
                <div className="flex space-x-8 px-6">
                  <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-blue-600 font-medium text-sm text-blue-600">
                    <FiUser size={16} />
                    All Trainers
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {trainers.length === 0 ? (
                    <div className="text-center py-12">
                      <FiUser className="text-6xl text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500 text-lg">No trainers added yet</p>
                      <p className="text-gray-400 text-sm mt-2">Add your first trainer using the form</p>
                    </div>
                  ) : (
                    trainers.map((trainer) => (
                      <div key={trainer.id} className="bg-gray-50 rounded-xl p-6 border-2 border-black hover:shadow-lg transition-all duration-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-4">
                              <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white shadow-lg">
                                {trainer.specialization}
                              </span>
                              <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                                {trainer.batches} Batches
                              </span>
                            </div>
                            <h4 className="text-xl font-bold text-gray-900 mb-3">{trainer.name}</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiMail className="mr-3 text-blue-500" />
                                <span className="font-medium">{trainer.email}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiBookOpen className="mr-3 text-purple-500" />
                                <span className="font-medium">{trainer.batches} Batches</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <button
                              onClick={() => openTrainerModal(trainer)}
                              className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors border-2 border-blue-600"
                            >
                              <FiEye className="text-lg" />
                            </button>
                            <button
                              className="bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors border-2 border-green-600"
                            >
                              <FiEdit2 className="text-lg" />
                            </button>
                            <button
                              onClick={() => handleDeleteTrainer(trainer.id)}
                              className="bg-red-600 text-white p-2 rounded-lg hover:bg-red-700 transition-colors border-2 border-red-600"
                            >
                              <FiTrash2 className="text-lg" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Trainer Details Modal */}
      {showModal && selectedTrainer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 border-2 border-black">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Trainer Details</h3>
              <button
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700 text-2xl"
              >
                ×
              </button>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white">
                  {selectedTrainer.specialization}
                </span>
                <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                  {selectedTrainer.batches} Batches
                </span>
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4">{selectedTrainer.name}</h4>
              <div className="space-y-3">
                <div className="flex items-center text-gray-700">
                  <FiMail className="mr-3 text-green-500" />
                  <span className="font-medium">{selectedTrainer.email}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiBookOpen className="mr-3 text-purple-500" />
                  <span className="font-medium">{selectedTrainer.batches} Batches</span>
                </div>
                {selectedTrainer.profilePic && (
                  <div className="flex items-center text-gray-700">
                    <FiImage className="mr-3 text-teal-500" />
                    <img src={selectedTrainer.profilePic} alt="Profile" className="w-16 h-16 rounded-full object-cover border-2 border-gray-200" />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Trainer; 