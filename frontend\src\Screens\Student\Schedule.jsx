import React, { useState } from "react";
import { <PERSON>Calendar, FiClock, FiMapPin, FiUser, FiBook, FiGrid, FiList } from "react-icons/fi";

const defaultSchedule = [
  {
    id: 1,
    day: "Monday",
    time: "09:00 AM - 10:30 AM",
    subject: "Mathematics",
    courseCode: "MATH101",
    faculty: "<PERSON><PERSON> <PERSON>",
    room: "Room 101",
    type: "Lecture",
    color: "bg-blue-500"
  },
  {
    id: 2,
    day: "Monday",
    time: "11:00 AM - 12:30 PM",
    subject: "Physics",
    courseCode: "PHY102",
    faculty: "<PERSON><PERSON> <PERSON>",
    room: "Room 205",
    type: "Lab",
    color: "bg-green-500"
  },
  {
    id: 3,
    day: "Tuesday",
    time: "09:00 AM - 10:30 AM",
    subject: "Computer Science",
    courseCode: "CS103",
    faculty: "<PERSON><PERSON> <PERSON>",
    room: "Room 301",
    type: "Lecture",
    color: "bg-purple-500"
  },
  {
    id: 4,
    day: "Wednesday",
    time: "02:00 PM - 03:30 PM",
    subject: "English",
    courseCode: "<PERSON>NG104",
    faculty: "<PERSON><PERSON> <PERSON>",
    room: "Room 102",
    type: "Tutorial",
    color: "bg-orange-500"
  },
  {
    id: 5,
    day: "Thursday",
    time: "10:00 AM - 11:30 AM",
    subject: "Chemistry",
    courseCode: "CHEM105",
    faculty: "Dr. <PERSON>",
    room: "Lab 201",
    type: "Lab",
    color: "bg-red-500"
  },
  {
    id: 6,
    day: "Friday",
    time: "09:00 AM - 10:30 AM",
    subject: "Engineering Drawing",
    courseCode: "ED106",
    faculty: "Prof. Brown",
    room: "Room 401",
    type: "Lecture",
    color: "bg-indigo-500"
  },
  {
    id: 7,
    day: "Friday",
    time: "02:00 PM - 04:00 PM",
    subject: "AutoCAD",
    courseCode: "ACAD107",
    faculty: "Prof. Garcia",
    room: "Computer Lab 1",
    type: "Lab",
    color: "bg-teal-500"
  }
];

const daysOfWeek = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
const timeSlots = [
  "08:00 AM", "09:00 AM", "10:00 AM", "11:00 AM", "12:00 PM", 
  "01:00 PM", "02:00 PM", "03:00 PM", "04:00 PM", "05:00 PM"
];

const Schedule = () => {
  const [schedule, setSchedule] = useState(defaultSchedule);
  const [selectedDay, setSelectedDay] = useState("Monday");
  const [viewMode, setViewMode] = useState("calendar"); // "calendar" or "list"
  const [selectedClass, setSelectedClass] = useState(null);
  const [showClassModal, setShowClassModal] = useState(false);

  const filteredSchedule = schedule.filter(item => item.day === selectedDay);

  const getTypeColor = (type) => {
    switch (type) {
      case "Lecture": return "bg-blue-500";
      case "Lab": return "bg-green-500";
      case "Tutorial": return "bg-purple-500";
      case "Seminar": return "bg-orange-500";
      default: return "bg-gray-500";
    }
  };

  const getClassForTimeSlot = (day, time) => {
    return schedule.find(item => item.day === day && item.time.includes(time));
  };

  const getClassesForDay = (day) => {
    return schedule.filter(item => item.day === day);
  };

  return (
    <div className="w-full min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Class Schedule</h1>
            <p className="text-gray-700 mt-1">Manage your weekly class timetable</p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => setViewMode("calendar")}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                viewMode === "calendar"
                  ? "bg-blue-600 text-white border-2 border-blue-600"
                  : "bg-gray-100 border-2 border-black text-black hover:bg-gray-200"
              }`}
            >
              <FiGrid className="text-lg" />
              Calendar View
            </button>
            <button
              onClick={() => setViewMode("list")}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                viewMode === "list"
                  ? "bg-blue-600 text-white border-2 border-blue-600"
                  : "bg-gray-100 border-2 border-black text-black hover:bg-gray-200"
              }`}
            >
              <FiList className="text-lg" />
              List View
            </button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-700 text-sm font-medium">Total Classes</p>
                <p className="text-3xl font-bold text-gray-900">{schedule.length}</p>
              </div>
              <FiCalendar className="text-4xl text-blue-500" />
            </div>
          </div>
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-700 text-sm font-medium">This Week</p>
                <p className="text-3xl font-bold text-gray-900">{filteredSchedule.length}</p>
              </div>
              <FiClock className="text-4xl text-purple-500" />
            </div>
          </div>
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-700 text-sm font-medium">Lectures</p>
                <p className="text-3xl font-bold text-gray-900">{schedule.filter(item => item.type === "Lecture").length}</p>
              </div>
              <FiBook className="text-4xl text-green-500" />
            </div>
          </div>
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-700 text-sm font-medium">Labs</p>
                <p className="text-3xl font-bold text-gray-900">{schedule.filter(item => item.type === "Lab").length}</p>
              </div>
              <FiUser className="text-4xl text-orange-500" />
            </div>
          </div>
        </div>

        {/* Main Content */}
        {viewMode === "calendar" ? (
          /* Calendar View */
          <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black overflow-hidden">
            {/* Day Selector */}
            <div className="bg-gray-100 p-4 border-b-2 border-black">
              <div className="flex flex-wrap gap-2">
                {daysOfWeek.map((day) => (
                  <button
                    key={day}
                    onClick={() => setSelectedDay(day)}
                    className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                      selectedDay === day
                        ? "bg-blue-600 text-white border-2 border-blue-600 shadow-lg"
                        : "bg-gray-50 border-2 border-black text-gray-900 hover:bg-gray-200"
                    }`}
                  >
                    {day}
                  </button>
                ))}
              </div>
            </div>

            {/* Calendar Grid */}
            <div className="p-6">
              <div className="grid grid-cols-8 gap-4">
                {/* Time column */}
                <div className="space-y-4">
                  <div className="h-12"></div> {/* Header spacer */}
                  {timeSlots.map((time) => (
                    <div key={time} className="h-20 flex items-center justify-center text-sm text-gray-700 font-medium">
                      {time}
                    </div>
                  ))}
                </div>

                {/* Days columns */}
                {daysOfWeek.map((day) => (
                  <div key={day} className="space-y-4">
                    <div className="h-12 flex items-center justify-center">
                      <h3 className="text-lg font-semibold text-gray-900">{day}</h3>
                    </div>
                    {timeSlots.map((time) => {
                      const classItem = getClassForTimeSlot(day, time);
                      return (
                        <div key={time} className="h-20 border-2 border-black rounded-lg p-2 relative">
                          {classItem ? (
                            <div
                              className={`w-full h-full rounded-lg p-2 cursor-pointer transition-all duration-200 hover:scale-105 ${
                                classItem.color
                              } text-white shadow-lg`}
                              onClick={() => {
                                setSelectedClass(classItem);
                                setShowClassModal(true);
                              }}
                            >
                              <div className="text-xs font-bold mb-1">{classItem.type}</div>
                              <div className="text-xs font-medium truncate">{classItem.subject}</div>
                              <div className="text-xs opacity-90">{classItem.room}</div>
                            </div>
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-gray-400">
                              <div className="text-xs">-</div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          /* List View */
          <div className="space-y-6">
            {/* Day Tabs */}
            <div className="flex flex-wrap gap-2">
              {daysOfWeek.map((day) => (
                <button
                  key={day}
                  onClick={() => setSelectedDay(day)}
                  className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                    selectedDay === day
                      ? "bg-blue-600 text-white border-2 border-blue-600 shadow-lg"
                      : "bg-gray-100 border-2 border-black text-black hover:bg-gray-200"
                  }`}
                >
                  {day}
                </button>
              ))}
            </div>

            {/* Classes for Selected Day */}
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">{selectedDay} Classes</h3>
              {getClassesForDay(selectedDay).length === 0 ? (
                <div className="text-center py-12">
                  <FiCalendar className="text-6xl text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">No classes scheduled for {selectedDay}</p>
                  <p className="text-gray-400 text-sm mt-2">No classes available for this day</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {getClassesForDay(selectedDay)
                    .sort((a, b) => a.time.localeCompare(b.time))
                    .map((classItem) => (
                    <div key={classItem.id} className="bg-gray-50 rounded-xl p-6 border-2 border-black hover:shadow-lg transition-all duration-200">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-4">
                            <span className={`${getTypeColor(classItem.type)} text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white shadow-lg`}>
                              {classItem.type}
                            </span>
                            <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                              {classItem.courseCode}
                            </span>
                          </div>
                          <h4 className="text-xl font-bold text-gray-900 mb-3">{classItem.subject}</h4>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                              <FiClock className="mr-3 text-blue-500" />
                              <span className="font-medium">{classItem.time}</span>
                            </div>
                            <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                              <FiUser className="mr-3 text-green-500" />
                              <span className="font-medium">{classItem.faculty}</span>
                            </div>
                            <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                              <FiMapPin className="mr-3 text-purple-500" />
                              <span className="font-medium">{classItem.room}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Class Details Modal */}
        {showClassModal && selectedClass && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 border-2 border-black">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Class Details</h3>
                <button
                  onClick={() => setShowClassModal(false)}
                  className="text-gray-500 hover:text-gray-700 text-2xl"
                >
                  ×
                </button>
              </div>
              <div className="space-y-4">
                <div className="flex items-center gap-3 mb-4">
                  <span className={`${getTypeColor(selectedClass.type)} text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white`}>
                    {selectedClass.type}
                  </span>
                  <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                    {selectedClass.courseCode}
                  </span>
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-4">{selectedClass.subject}</h4>
                <div className="space-y-3">
                  <div className="flex items-center text-gray-700">
                    <FiClock className="mr-3 text-blue-500" />
                    <span className="font-medium">{selectedClass.time}</span>
                  </div>
                  <div className="flex items-center text-gray-700">
                    <FiUser className="mr-3 text-green-500" />
                    <span className="font-medium">{selectedClass.faculty}</span>
                  </div>
                  <div className="flex items-center text-gray-700">
                    <FiMapPin className="mr-3 text-purple-500" />
                    <span className="font-medium">{selectedClass.room}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Schedule; 
