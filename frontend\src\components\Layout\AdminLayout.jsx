import React, { useState, useEffect } from 'react';
import { FiMenu, FiX, FiHome, FiUsers, FiBook, FiCalendar, FiSettings, FiLogOut, FiUser, FiBookOpen, FiClock, FiLayers, FiUpload, FiBarChart2, FiAward } from 'react-icons/fi';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import { theme } from '../../theme';
import styled from 'styled-components';

// Styled components for better organization
const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: ${theme.colors.background};
  color: ${theme.colors.text.primary};
`;

const Sidebar = styled.aside`
  width: 250px;
  background-color: ${theme.colors.surface};
  border-right: 1px solid ${theme.colors.border};
  position: fixed;
  top: 0;
  left: ${({ $isOpen }) => ($isOpen ? '0' : '-250px')};
  bottom: 0;
  z-index: 50;
  transition: left 0.3s ease;
  overflow-y: auto;
  
  @media (min-width: ${theme.breakpoints.tablet}) {
    left: 0;
  }
`;

const SidebarHeader = styled.div`
  padding: ${theme.spacing.lg};
  border-bottom: 1px solid ${theme.colors.border};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const AdminPanelTitle = styled(Link)`
  color: ${theme.colors.text.primary};
  text-decoration: none;
  font-size: 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: color 0.2s ease;
  
  &:hover {
    color: ${theme.colors.secondary};
  }
`;

const MenuButton = styled.button`
  background: none;
  border: none;
  color: #222; /* Ensures high-contrast dark icon */
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${theme.spacing.sm};
  border-radius: 50%;
  transition: all 0.2s ease;
  &:hover {
    background-color: rgba(30, 64, 175, 0.08); /* subtle blue hover */
    color: #1e40af; /* blue-800 on hover */
  }
  @media (min-width: ${theme.breakpoints.tablet}) {
    display: none;
  }
`;

const MenuList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const MenuItem = styled.li`
  margin-bottom: ${theme.spacing.sm};
`;

const MenuLink = styled(Link)`
  display: flex;
  align-items: center;
  padding: ${theme.spacing.sm} ${theme.spacing.md};
  color: ${({ $isActive }) => $isActive ? theme.colors.secondary : theme.colors.text.primary};
  text-decoration: none;
  border-radius: ${theme.borderRadius.md};
  background-color: ${({ $isActive }) => $isActive ? 'rgba(255, 255, 255, 0.1)' : 'transparent'};
  transition: all 0.2s ease;
  margin: 0 ${theme.spacing.sm};
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  svg {
    margin-right: ${theme.spacing.sm};
  }
`;

const MainContent = styled.main`
  flex: 1;
  margin-left: 0;
  padding: ${theme.spacing.lg};
  transition: margin-left 0.3s ease;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  
  @media (min-width: ${theme.breakpoints.tablet}) {
    margin-left: 250px;
    width: calc(100% - 250px);
  }
`;

const MobileHeader = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${theme.spacing.lg};
  padding: ${theme.spacing.sm} 0;
  border-bottom: 1px solid ${theme.colors.border};
  
  h1 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
  }
  
  @media (min-width: ${theme.breakpoints.tablet}) {
    display: none;
  }
`;

const adminMenuConfig = [
  { file: 'CreateBatch.jsx', label: 'Create Batch', icon: <FiLayers size={20} />, path: '/admin/create-batch' },
  { file: 'Trainer.jsx', label: 'Trainer', icon: <FiUser size={20} />, path: '/admin/trainer' },
  { file: 'College.jsx', label: 'College', icon: <FiHome size={20} />, path: '/admin/college' },
  { file: 'AssignBatchToTrainers.jsx', label: 'Assign Batch to Trainers', icon: <FiUsers size={20} />, path: '/admin/assign-batch' },
  { file: 'UploadCourseWithTrainer.jsx', label: 'Upload Course With Trainer', icon: <FiUpload size={20} />, path: '/admin/upload-course' },
  { file: 'ViewBatchWiseReports.jsx', label: 'View Batch-wise Reports', icon: <FiBarChart2 size={20} />, path: '/admin/batch-reports' },
  { file: 'ViewUserReports.jsx', label: 'View User Reports', icon: <FiBarChart2 size={20} />, path: '/admin/user-reports' },
  { file: 'UploadCertificate.jsx', label: 'Upload Certificate', icon: <FiAward size={20} />, path: '/admin/upload-certificate' },
];

const AdminLayout = ({ children }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const navigate = useNavigate();
  const location = useLocation();

  // Check if device is mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth >= 768) {
        setIsSidebarOpen(true);
      } else {
        setIsSidebarOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initial check
    
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Dynamically filter menu items based on files present in Admin folder
  const menuItems = adminMenuConfig.filter(item => [
    'CreateBatch.jsx',
    'Trainer.jsx',
    'College.jsx',
    'AssignBatchToTrainers.jsx',
    'UploadCourseWithTrainer.jsx',
    'ViewBatchWiseReports.jsx',
    'ViewUserReports.jsx',
    'UploadCertificate.jsx',
  ].includes(item.file));

  const handleLogout = () => {
    // Clear any authentication tokens or user data
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    // Redirect to login page
    navigate('/login');
    
    // Close sidebar on mobile after navigation
    if (isMobile) {
      setIsSidebarOpen(false);
    }
  };

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isMobile && isSidebarOpen && !event.target.closest('aside')) {
        setIsSidebarOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobile, isSidebarOpen]);

  return (
    <LayoutContainer>
      {/* Mobile Sidebar Overlay */}
      {isMobile && isSidebarOpen && (
        <div 
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            zIndex: 40,
            display: 'block',
          }}
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <Sidebar $isOpen={isSidebarOpen}>
        <SidebarHeader>
          <AdminPanelTitle to="/admin">
            Admin Panel
          </AdminPanelTitle>
          <MenuButton onClick={() => setIsSidebarOpen(false)}>
            <FiX size={24} />
          </MenuButton>
        </SidebarHeader>

        <nav>
          <MenuList>
            {menuItems.map((item) => (
              <MenuItem key={item.path}>
                <MenuLink
                  to={item.path}
                  $isActive={location.pathname === item.path}
                  onClick={() => isMobile && setIsSidebarOpen(false)}
                >
                  {item.icon}
                  {item.label}
                </MenuLink>
              </MenuItem>
            ))}
          </MenuList>
        </nav>

        <div style={{
          padding: theme.spacing.md,
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          borderTop: `1px solid ${theme.colors.border}`,
        }}>
          <MenuLink 
            as="button"
            onClick={handleLogout}
            style={{
              width: '100%',
              textAlign: 'left',
              cursor: 'pointer',
            }}
          >
            <FiLogOut />
            Logout
          </MenuLink>
        </div>
      </Sidebar>

      {/* Main Content */}
      <MainContent>
        {/* Mobile Header */}
        {isMobile && (
          <MobileHeader>
            <MenuButton onClick={() => setIsSidebarOpen(true)}>
              <FiMenu size={24} />
            </MenuButton>
            <h1>
              {menuItems.find(item => item.path === location.pathname)?.label || 'Dashboard'}
            </h1>
            <div style={{ width: '40px' }}></div> {/* Spacer for alignment */}
          </MobileHeader>
        )}

        {/* Page Content */}
        <div style={{
          backgroundColor: theme.colors.background,
          borderRadius: theme.borderRadius.lg,
          padding: isMobile ? theme.spacing.md : theme.spacing.lg,
          minHeight: 'calc(100vh - 120px)',
        }}>
          {children}
        </div>
      </MainContent>
    </LayoutContainer>
  );
};

export default AdminLayout;
