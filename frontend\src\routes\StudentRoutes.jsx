import React from "react";
import { Routes, Route } from "react-router-dom";
import StudentLayout from "../components/Layout/StudentLayout";
import Home from "../Screens/Student/Home";
import Schedule from "../Screens/Student/Schedule";
import MyCourses from "../Screens/Student/MyCourses";

import Profile from "../Screens/Student/Profile";

import Certificate from "../Screens/Student/Certificate";

const StudentRoutes = () => (
  <StudentLayout>
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/schedule" element={<Schedule />} />
      <Route path="/courses" element={<MyCourses />} />

      <Route path="/profile" element={<Profile />} />

      <Route path="/certificate" element={<Certificate />} />
      {/* Add more student routes as needed */}
    </Routes>
  </StudentLayout>
);

export default StudentRoutes; 