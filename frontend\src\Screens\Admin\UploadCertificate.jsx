import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>sers, 
  FiBookOpen,
  FiCalendar,
  FiUpload,
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiEye,
  FiX,
  FiSave,
  FiRefreshCw,
  FiFile,
  FiDownload,
  FiCheckCircle,
  FiMapPin
} from 'react-icons/fi';

const studentOptions = ['<PERSON>', '<PERSON>', '<PERSON>'];
const batchOptions = ['Batch A – 2024', 'Batch B – 2025'];
const courseOptions = ['Python', 'Math', 'AI'];

const initialCertificates = [
  {
    id: 1,
    name: 'Java Completion Certificate',
    targetType: 'Student',
    target: '<PERSON>',
    course: 'Python',
    issueDate: '2024-07-01',
    file: 'java-certificate.pdf',
  },
];

const UploadCertificate = () => {
  const [name, setName] = useState('');
  const [targetType, setTargetType] = useState('Student');
  const [target, setTarget] = useState('');
  const [course, setCourse] = useState('');
  const [issueDate, setIssueDate] = useState('');
  const [file, setFile] = useState('');
  const [certificates, setCertificates] = useState(initialCertificates);
  const [showModal, setShowModal] = useState(false);
  const [selectedCertificate, setSelectedCertificate] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    const newCertificate = {
      id: Date.now(),
      name,
      targetType,
      target,
      course,
      issueDate,
      file: file ? file.name : '',
    };
    setCertificates([...certificates, newCertificate]);
    handleReset();
  };

  const handleReset = () => {
    setName('');
    setTarget('');
    setCourse('');
    setIssueDate('');
    setFile('');
  };

  const openCertificateModal = (certificate) => {
    setSelectedCertificate(certificate);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedCertificate(null);
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  return (
    <div className="p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Upload Certificate</h1>
            <p className="text-gray-700 mt-1">Create and manage certificate uploads</p>
          </div>
          <button 
            onClick={handleEditToggle}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
              isEditing 
                ? "bg-red-600 text-white hover:bg-red-700" 
                : "bg-blue-600 text-white hover:bg-blue-700"
            }`}
          >
            {isEditing ? <FiX size={16} /> : <FiPlus size={16} />}
            {isEditing ? "Cancel" : "Edit Mode"}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Certificate Upload Form */}
          <div className="lg:col-span-1">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Upload New Certificate</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiAward className="mr-2 text-blue-500" />
                    Certificate Name
                  </label>
                  <input 
                    value={name} 
                    onChange={e => setName(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                    placeholder="Certificate Name" 
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiUser className="mr-2 text-yellow-500" />
                    Target Type
                  </label>
                  <div className="flex gap-4 items-center bg-gray-50 border-2 border-black rounded-lg p-3">
                    <label className="flex items-center gap-2 text-gray-700 font-medium">
                      <input 
                        type="radio" 
                        value="Student" 
                        checked={targetType === 'Student'} 
                        onChange={() => setTargetType('Student')}
                        className="text-blue-500 focus:ring-blue-500"
                      /> 
                      <FiUser className="text-yellow-500" />
                      Student
                    </label>
                    <label className="flex items-center gap-2 text-gray-700 font-medium">
                      <input 
                        type="radio" 
                        value="Batch" 
                        checked={targetType === 'Batch'} 
                        onChange={() => setTargetType('Batch')}
                        className="text-blue-500 focus:ring-blue-500"
                      /> 
                      <FiUsers className="text-green-500" />
                      Batch
                    </label>
                  </div>
                  <div className="mt-3">
                    {targetType === 'Student' ? (
                      <select 
                        value={target} 
                        onChange={e => setTarget(e.target.value)} 
                        required 
                        className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Select Student</option>
                        {studentOptions.map(s => <option key={s} value={s}>{s}</option>)}
                      </select>
                    ) : (
                      <select 
                        value={target} 
                        onChange={e => setTarget(e.target.value)} 
                        required 
                        className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Select Batch</option>
                        {batchOptions.map(b => <option key={b} value={b}>{b}</option>)}
                      </select>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiBookOpen className="mr-2 text-green-500" />
                    Select Course
                  </label>
                  <select 
                    value={course} 
                    onChange={e => setCourse(e.target.value)} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Course</option>
                    {courseOptions.map(c => <option key={c} value={c}>{c}</option>)}
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiCalendar className="mr-2 text-purple-500" />
                    Issue Date
                  </label>
                  <input 
                    type="date" 
                    value={issueDate} 
                    onChange={e => setIssueDate(e.target.value)} 
                    required 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                  />
                </div>

                <div>
                  <label className="block text-gray-700 text-sm font-bold mb-2 flex items-center">
                    <FiFile className="mr-2 text-orange-500" />
                    Upload File (PDF)
                  </label>
                  <input 
                    type="file" 
                    accept="application/pdf" 
                    onChange={e => setFile(e.target.files[0])} 
                    className="w-full bg-gray-50 border-2 border-black rounded-lg px-4 py-3 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <button 
                    type="submit" 
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiUpload className="mr-2" />
                    Upload Certificate
                  </button>
                  <button 
                    type="button" 
                    onClick={handleReset} 
                    className="bg-gray-100 border-2 border-black text-black px-6 py-3 rounded-lg font-bold transition-all duration-200 flex items-center hover:bg-gray-200"
                  >
                    <FiRefreshCw className="mr-2" />
                    Reset
                  </button>
                </div>
              </form>
            </div>
          </div>

          {/* Right Panel - Certificate List */}
          <div className="lg:col-span-2">
            <div className="bg-gray-50 rounded-xl shadow-lg border-2 border-black">
              <div className="border-b border-gray-200">
                <div className="flex space-x-8 px-6">
                  <button className="flex items-center gap-2 py-4 px-1 border-b-2 border-blue-600 font-medium text-sm text-blue-600">
                    <FiAward size={16} />
                    Uploaded Certificates
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {certificates.length === 0 ? (
                    <div className="text-center py-12">
                      <FiAward className="text-6xl text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500 text-lg">No certificates uploaded yet</p>
                      <p className="text-gray-400 text-sm mt-2">Upload your first certificate using the form</p>
                    </div>
                  ) : (
                    certificates.map((certificate) => (
                      <div key={certificate.id} className="bg-gray-50 rounded-xl p-6 border-2 border-black hover:shadow-lg transition-all duration-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-4">
                              <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white shadow-lg">
                                {certificate.targetType}
                              </span>
                              <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                                {certificate.course}
                              </span>
                            </div>
                            <h4 className="text-xl font-bold text-gray-900 mb-3">{certificate.name}</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                {certificate.targetType === 'Student' ? (
                                  <FiUser className="mr-3 text-yellow-500" />
                                ) : (
                                  <FiUsers className="mr-3 text-green-500" />
                                )}
                                <span className="font-medium">{certificate.target}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiBookOpen className="mr-3 text-green-500" />
                                <span className="font-medium">{certificate.course}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiCalendar className="mr-3 text-purple-500" />
                                <span className="font-medium">{certificate.issueDate}</span>
                              </div>
                              <div className="flex items-center text-gray-700 bg-gray-100 p-3 rounded-lg border-2 border-black">
                                <FiFile className="mr-3 text-orange-500" />
                                <span className="font-medium">{certificate.file}</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <button
                              onClick={() => openCertificateModal(certificate)}
                              className="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors border-2 border-blue-600"
                            >
                              <FiEye className="text-lg" />
                            </button>
                            <button
                              className="bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors border-2 border-green-600"
                            >
                              <FiEdit2 className="text-lg" />
                            </button>
                            <button
                              className="bg-red-600 text-white p-2 rounded-lg hover:bg-red-700 transition-colors border-2 border-red-600"
                            >
                              <FiTrash2 className="text-lg" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Certificate Details Modal */}
      {showModal && selectedCertificate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 border-2 border-black">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Certificate Details</h3>
              <button
                onClick={closeModal}
                className="text-gray-500 hover:text-gray-700 text-2xl"
              >
                ×
              </button>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-bold border-2 border-white">
                  {selectedCertificate.targetType}
                </span>
                <span className="text-sm text-gray-700 bg-gray-100 px-3 py-1 rounded-lg border-2 border-black">
                  {selectedCertificate.course}
                </span>
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4">{selectedCertificate.name}</h4>
              <div className="space-y-3">
                <div className="flex items-center text-gray-700">
                  {selectedCertificate.targetType === 'Student' ? (
                    <FiUser className="mr-3 text-yellow-500" />
                  ) : (
                    <FiUsers className="mr-3 text-green-500" />
                  )}
                  <span className="font-medium">{selectedCertificate.target}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiBookOpen className="mr-3 text-green-500" />
                  <span className="font-medium">{selectedCertificate.course}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiCalendar className="mr-3 text-purple-500" />
                  <span className="font-medium">Issue Date: {selectedCertificate.issueDate}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiFile className="mr-3 text-orange-500" />
                  <span className="font-medium">File: {selectedCertificate.file}</span>
                </div>
                <div className="flex items-center text-gray-700">
                  <FiCheckCircle className="mr-3 text-green-500" />
                  <span className="font-medium">Status: Issued</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UploadCertificate; 